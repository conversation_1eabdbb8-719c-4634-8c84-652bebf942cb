import { faPlay, faPause, faStop } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Col, message, Row, Tooltip } from 'antd';
import { ReactNode, useEffect } from 'react';
import { rootStore } from '@store/instanse';
import { observer } from 'mobx-react';
import { IngamePermissions } from 'types/ingame';
import { useReactive } from 'ahooks';
import { TSessionAssignmentInfo } from 'types/session/sessionAssignmentInfo';

import './layout.scss';

type IngameLayoutProps = {
    additionalClass?: string;
    paddingNone?: boolean;
    showTopBar: boolean;
    topBarText: string | null;
    children: ReactNode;
};

type TState = {
    permissions: IngamePermissions | null;
    SAI: TSessionAssignmentInfo | null;
};

const IngameContentLayout = observer(
    ({ paddingNone = false, showTopBar, topBarText, children }: IngameLayoutProps): JSX.Element => {
        const state = useReactive<TState>({
            permissions: null,
            SAI: null,
        });

        async function init() {
            message.destroy('assignment-notice');
            state.permissions = rootStore.ingameStore.getPermissions();
            if (rootStore.ingameStore.getInitStatus()) {
                state.SAI = await rootStore.ingameStore.getInfo();
            }
        }

        useEffect(() => {
            init();
        }, []);

        useEffect(() => {
            state.SAI = rootStore.ingameStore.SAI;
        }, [
            rootStore.ingameStore.SAI?.config?.state,
            rootStore.ingameStore.SAI?.config?.day,
            rootStore.ingameStore.SAI?.config?.tick,
        ]);

        async function start() {
            await rootStore.ingameStore.stateStart();
            await rootStore.ingameStore.getInfo(true);
        }

        async function pause() {
            await rootStore.ingameStore.statePause();
            await rootStore.ingameStore.getInfo(true);
        }

        async function resume() {
            await rootStore.ingameStore.stateResume();
            await rootStore.ingameStore.getInfo(true);
        }

        async function stop() {
            await rootStore.ingameStore.stateStop();
            await rootStore.ingameStore.getInfo(true);
        }

        useEffect(() => {
            const handleBeforeUnload = (event) => {
                if (rootStore.ingameStore.SAI?.config?.state != 'finished') {
                    event.preventDefault();
                    const message = 'Остались несохранённые изменения!';
                    event.returnValue = message; // For most browsers
                    return message; // For some older versions
                }
            };

            window.addEventListener('beforeunload', handleBeforeUnload);

            return () => {
                window.removeEventListener('beforeunload', handleBeforeUnload);
            };
        }, [rootStore.ingameStore.SAI?.config?.state]);

        async function reinitPerms() {
            if (!rootStore.ingameStore.getInitStatus()) return;
            await rootStore.ingameStore.initPermissions();
            state.permissions = rootStore.ingameStore.getPermissions();
        }

        useEffect(() => {
            reinitPerms();
        }, [
            rootStore.ingameStore.sessionAssignment,
            rootStore.currentUserStore.user,
        ]);

        return (
            <div
                className={`main-container${rootStore.ingameStore.sideBarMinimized ? ' sbm' : ''}`}
            >
                {showTopBar && (
                    <Row className={`top-bar ${topBarText == null ? 'left-side' : 'spaced'}`}>
                        {topBarText != null && (
                            <Col>
                                <h4>{topBarText}</h4>
                            </Col>
                        )}
                        <Col className="time-and-controls">
                            <Row>
                                <Col className="controls">
                                    {state.SAI?.config?.state == 'prestarted' && (
                                        <Row>
                                            <Tooltip
                                                placement="bottom"
                                                title="Запуск прохождения"
                                            >
                                                <Button
                                                    disabled={!state.permissions?.allowStart}
                                                    icon={<FontAwesomeIcon icon={faPlay} />}
                                                    onClick={start}
                                                />
                                            </Tooltip>
                                        </Row>
                                    )}
                                    {(state.SAI?.config?.state == 'started' ||
                                        state.SAI?.config?.state == 'resumed') && (
                                        <Row>
                                            <Tooltip
                                                placement="bottom"
                                                title="Прохождение идёт"
                                            >
                                                <Button
                                                    icon={<FontAwesomeIcon icon={faPlay} />}
                                                    type="primary"
                                                />
                                            </Tooltip>
                                            <Tooltip
                                                placement="bottom"
                                                title="Поставить на паузу"
                                            >
                                                <Button
                                                    disabled={!state.permissions?.allowPause}
                                                    icon={<FontAwesomeIcon icon={faPause} />}
                                                    onClick={pause}
                                                />
                                            </Tooltip>
                                            <Tooltip
                                                placement="bottom"
                                                title="Приостановить"
                                            >
                                                <Button
                                                    disabled={!state.permissions?.allowStop}
                                                    icon={<FontAwesomeIcon icon={faStop} />}
                                                    onClick={stop}
                                                />
                                            </Tooltip>
                                        </Row>
                                    )}
                                    {state.SAI?.config?.state == 'paused' && (
                                        <Row>
                                            <Tooltip
                                                placement="bottom"
                                                title="Снять с паузы"
                                            >
                                                <Button
                                                    disabled={!state.permissions?.allowResume}
                                                    icon={<FontAwesomeIcon icon={faPlay} />}
                                                    onClick={resume}
                                                />
                                            </Tooltip>
                                            <Tooltip
                                                placement="bottom"
                                                title="На паузе"
                                            >
                                                <Button
                                                    icon={<FontAwesomeIcon icon={faPause} />}
                                                    type="primary"
                                                />
                                            </Tooltip>
                                            <Tooltip
                                                placement="bottom"
                                                title="Приостановить"
                                            >
                                                <Button
                                                    disabled
                                                    icon={<FontAwesomeIcon icon={faStop} />}
                                                    onClick={stop}
                                                />
                                            </Tooltip>
                                        </Row>
                                    )}
                                    {state.SAI?.config?.state == 'stopped' && (
                                        <Row>
                                            <Tooltip
                                                placement="bottom"
                                                title="Возобновить"
                                            >
                                                <Button
                                                    disabled={!state.permissions?.allowResume}
                                                    icon={<FontAwesomeIcon icon={faPlay} />}
                                                    onClick={resume}
                                                />
                                            </Tooltip>
                                            <Tooltip
                                                placement="bottom"
                                                title="Поставить на паузу"
                                            >
                                                <Button
                                                    disabled
                                                    icon={<FontAwesomeIcon icon={faPause} />}
                                                />
                                            </Tooltip>
                                            <Tooltip
                                                placement="bottom"
                                                title="Остановлено"
                                            >
                                                <Button
                                                    icon={<FontAwesomeIcon icon={faStop} />}
                                                    type="primary"
                                                />
                                            </Tooltip>
                                        </Row>
                                    )}
                                    {state.SAI?.config?.state == 'finished' && (
                                        <Row>
                                            <Tooltip
                                                placement="bottom"
                                                title="Прохождение завершено"
                                            >
                                                <Button
                                                    disabled
                                                    icon={<FontAwesomeIcon icon={faPlay} />}
                                                />
                                            </Tooltip>
                                            <Tooltip
                                                placement="bottom"
                                                title="Прохождение завершено"
                                            >
                                                <Button
                                                    disabled
                                                    icon={<FontAwesomeIcon icon={faPause} />}
                                                />
                                            </Tooltip>
                                            <Tooltip
                                                placement="bottom"
                                                title="Прохождение завершено"
                                            >
                                                <Button
                                                    icon={<FontAwesomeIcon icon={faStop} />}
                                                    type="primary"
                                                />
                                            </Tooltip>
                                        </Row>
                                    )}
                                </Col>
                                <Col className="p2 time">
                                    {rootStore.ingameStore.infoDayTickToString()}
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                )}
                <div
                    className={`child-container ${showTopBar ? '' : 'fill-top'}`}
                    style={paddingNone ? { padding: '0px' } : {}}
                >
                    {children}
                </div>
            </div>
        );
    },
);

export { IngameContentLayout as IngameLayout };
