@use '/src/styles/colors';
@use '/src/styles/icons';

#ingame-layout {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    min-height: 100vh;
    height: auto !important;

    .left-side-bar,
    .left-side-bar-mini {
        align-items: center;
        background: #fff;
        bottom: 0;
        box-shadow: 4px 0px 4px 0px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        justify-content: space-between;
        min-height: 100%;
        padding: 64px 24px 24px 24px;
        position: fixed;
        row-gap: 32px;
        top: 0;
        width: 248px;
        z-index: 100;

        .logo-outer-container {
            height: 140px;
        }
        .menu-container {
            align-items: flex-start;
            display: flex;
            flex-direction: column;
            flex-wrap: nowrap;
            height: 100%;
            justify-content: center;
            row-gap: 16px;
            overflow-x: hidden;
            overflow-y: scroll;
            scroll-snap-type: y mandatory;
            width: 168px;

            &::-webkit-scrollbar,
            &::-webkit-scrollbar-thumb {
                height: 2px;
                width: 2px;
                background: transparent;
            }

            .menu-item,
            .menu-item-active,
            .menu-item-disabled {
                align-items: center;
                align-self: stretch;
                cursor: pointer;
                display: flex;
                height: 32px;
                justify-content: center;

                .menu-item-inner {
                    align-items: center;
                    display: flex;
                    gap: 8px;

                    svg {
                        color: colors.$neutral950;
                        height: 24px;
                        width: 24px;
                    }
                    .menu-item-label {
                        color: colors.$neutral950;
                        display: flex;
                        flex-direction: column;
                        height: 24px;
                        justify-content: center;
                        width: 125px;
                    }
                    .menu-item-icon {
                        background-position: center center;
                        background-repeat: no-repeat;
                        background-size: contain;
                        height: 24px;
                        transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                        width: 24px;
                    }
                    .desktop-icon {
                        @include icons.icon-image('#272C35');
                    }
                    .dashboard-icon {
                        @include icons.icon-dashboard('#272C35');
                    }
                    .gantt-icon {
                        @include icons.icon-gantt('#272C35');
                    }
                    .calendar-icon {
                        @include icons.icon-calendar('#272C35');
                    }
                    .budget-icon {
                        @include icons.icon-budget('#272C35');
                    }
                    .workers-icon {
                        @include icons.icon-team('#272C35');
                    }
                    .graph-icon {
                        @include icons.icon-network('#272C35');
                    }
                    .charts-icon {
                        @include icons.icon-reports('#272C35');
                    }
                    .chats-icon {
                        @include icons.icon-chats('#272C35');
                    }
                    .minimize-icon {
                        @include icons.icon-arrow('#272C35');

                        &.minimized {
                            transform: rotate(180deg);
                        }
                    }
                }
                &:hover {
                    background-color: colors.$accentW50;
                    .menu-item-inner {
                        svg {
                            color: colors.$accentW900;
                        }
                        .menu-item-label {
                            color: colors.$accentW900;
                        }
                        .desktop-icon {
                            @include icons.icon-image('#1B5680');
                        }
                        .dashboard-icon {
                            @include icons.icon-dashboard('#1B5680');
                        }
                        .gantt-icon {
                            @include icons.icon-gantt('#1B5680');
                        }
                        .calendar-icon {
                            @include icons.icon-calendar('#1B5680');
                        }
                        .budget-icon {
                            @include icons.icon-budget('#1B5680');
                        }
                        .workers-icon {
                            @include icons.icon-team('#1B5680');
                        }
                        .graph-icon {
                            @include icons.icon-network('#1B5680');
                        }
                        .charts-icon {
                            @include icons.icon-reports('#1B5680');
                        }
                        .chats-icon {
                            @include icons.icon-chats('#1B5680');
                        }
                        .minimize-icon {
                            @include icons.icon-arrow('#1B5680');
                        }
                    }
                }
            }
            .menu-item-disabled,
            .menu-item-disabled:hover {
                .menu-item-inner {
                    svg {
                        color: colors.$black600;
                    }
                    .menu-item-label {
                        color: colors.$black600;
                    }
                    .dashboard-icon {
                        @include icons.icon-dashboard('#A7A7A7');
                    }
                    .gantt-icon {
                        @include icons.icon-gantt('#A7A7A7');
                    }
                    .calendar-icon {
                        @include icons.icon-calendar('#A7A7A7');
                    }
                    .budget-icon {
                        @include icons.icon-budget('#A7A7A7');
                    }
                    .workers-icon {
                        @include icons.icon-team('#A7A7A7');
                    }
                    .graph-icon {
                        @include icons.icon-network('#A7A7A7');
                    }
                    .charts-icon {
                        @include icons.icon-reports('#A7A7A7');
                    }
                    .chats-icon {
                        @include icons.icon-chats('#A7A7A7');
                    }
                    .minimize-icon {
                        @include icons.icon-arrow('#A7A7A7');
                    }
                }
            }
            .menu-item-blank,
            .menu-item-disabled {
                height: 32px;
            }
            .menu-item-active .menu-item-inner {
                svg {
                    color: colors.$accentW400;
                }
                .menu-item-label {
                    color: colors.$accentW500;
                }
                .dashboard-icon {
                    @include icons.icon-dashboard('#4EB5FF');
                }
                .gantt-icon {
                    @include icons.icon-gantt('#4EB5FF');
                }
                .calendar-icon {
                    @include icons.icon-calendar('#4EB5FF');
                }
                .budget-icon {
                    @include icons.icon-budget('#4EB5FF');
                }
                .workers-icon {
                    @include icons.icon-team('#4EB5FF');
                }
                .graph-icon {
                    @include icons.icon-network('#4EB5FF');
                }
                .charts-icon {
                    @include icons.icon-reports('#4EB5FF');
                }
                .chats-icon {
                    @include icons.icon-chats('#4EB5FF');
                }
                .minimize-icon {
                    @include icons.icon-arrow('#4EB5FF');
                }
            }
        }
    }
    .left-side-bar-mini {
        padding: 72px 8px 24px 8px;
        width: 96px;

        .menu-container {
            width: 80px;
        }
        .logo-outer-container {
            height: 132px;
        }
    }

    .main-container {
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        margin-left: 248px;
        min-height: 100vh;
        overflow: auto;
        position: relative;
        width: 100%;

        .top-bar {
            align-items: center;
            align-self: stretch;
            background: colors.$accentW0;
            box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: row;
            height: 64px;
            padding: 16px 48px 16px 48px;
            position: fixed;
            width: calc(100% - 248px);
            z-index: 110;

            &.left-side {
                justify-content: flex-start;
            }
            &.spaced {
                justify-content: space-between;
            }
            h4 {
                margin: 0;
            }
            .time-and-controls > .ant-row {
                align-items: center;
                column-gap: 16px;

                .controls > .ant-row {
                    align-items: center;
                    column-gap: 8px;

                    .ant-btn {
                        background: colors.$accentW0;
                        border: 2px solid colors.$neutral25;
                        border-radius: 4px;
                        color: colors.$neutral950;
                        height: 32px;
                        width: 32px;

                        svg {
                            color: colors.$neutral950;
                            font-size: 16px;
                        }
                        &:not(:disabled):hover {
                            background: colors.$accentW10;
                            border: 2px solid colors.$accentW10;
                            color: colors.$accentW500;

                            svg {
                                color: colors.$accentW500;
                            }
                        }
                        &:disabled {
                            background: colors.$neutral25;

                            svg {
                                color: colors.$neutral300;
                            }
                        }
                        &.ant-btn-primary {
                            border: 2px solid colors.$accentW300;

                            svg {
                                color: colors.$accentW400;
                            }
                        }
                    }
                }
            }
        }
        &.sbm {
            margin-left: 96px;

            .top-bar {
                width: calc(100% - 96px);
            }
        }
        .child-container {
            align-items: flex-start;
            background-color: colors.$neutral50;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            margin-top: 64px;
            min-height: calc(100% - 64px);
            padding: 24px;

            &.fill-top {
                margin-top: 0;
                min-height: 100%;
            }
        }
    }
}
