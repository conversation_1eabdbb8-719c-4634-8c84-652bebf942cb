import Logo from '@components/ui/logo/logo';
import { faHome } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useMergedRouteHandle } from '@hooks/useMergedRouteHandle';
import { rootStore } from '@store/instanse';
import { Tooltip } from 'antd';
import { observer } from 'mobx-react';
import { Suspense, useRef } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import useDraggableScroll from 'use-draggable-scroll';

export const IngameUniLayout = observer(() => {
    const handle = useMergedRouteHandle();
    const navigate = useNavigate();
    const location = useLocation();
    const menuGroupRef = useRef<HTMLDivElement>(null);
    const { onMouseDown } = useDraggableScroll(menuGroupRef, {
        direction: 'vertical',
    });

    const menuEntries = [
        {
            id: 'desktop',
            icon: <div className="menu-item-icon desktop-icon" />,
            label: 'Рабочий стол',
            active: () => false,
            onClick: () => {
                navigate('/session/desktop');
            },
        },
        {
            id: 'dashboard',
            icon: <div className="menu-item-icon dashboard-icon" />,
            label: 'Дашборд',
            active: (path) => path.includes('/dashboard'),
            onClick: () => {
                navigate(`/session/dashboard`);
            },
        },
        {
            id: 'gantt',
            icon: <div className="menu-item-icon gantt-icon" />,
            label: 'Гант',
            active: (path) => path.includes('/gantt') || path.includes('/tasks'),
            onClick: () => {
                navigate(`/session/gantt`);
            },
        },
        {
            id: 'calendar',
            icon: <div className="menu-item-icon calendar-icon" />,
            label: 'Календарь',
            active: (path) => path.includes('/calendar'),
            onClick: () => {
                navigate(`/session/calendar`);
            },
        },
        {
            id: 'budget',
            icon: <div className="menu-item-icon budget-icon" />,
            label: 'Бюджет',
            active: (path) => path.includes('/budget'),
            onClick: () => {
                navigate(`/session/budget`);
            },
        },
        {
            id: 'workers',
            icon: <div className="menu-item-icon workers-icon" />,
            label: 'Команда',
            active: (path) => path.includes('/workers'),
            onClick: () => {
                navigate(`/session/workers`);
            },
        },
        {
            id: 'graph',
            icon: <div className="menu-item-icon graph-icon" />,
            label: 'Сеть',
            active: (path) => path.includes('/graph'),
            onClick: () => {
                navigate(`/session/graph`);
            },
        },
        {
            id: 'charts',
            icon: <div className="menu-item-icon charts-icon" />,
            label: 'Отчёты',
            active: (path) => path.includes('/charts'),
            onClick: () => {
                navigate(`/session/charts`);
            },
        },
        {
            id: 'chats',
            icon: <div className="menu-item-icon chats-icon" />,
            label: 'Чаты',
            active: (path) => path.includes('/chats'),
            onClick: () => {
                navigate(`/session/chats`);
            },
        },
        {
            id: 'home',
            icon: <FontAwesomeIcon icon={faHome} />,
            label: 'Домой',
            active: null,
            onClick: () => {
                rootStore.ingameStore.leftMenuScrollPosition = 0;
                navigate('/controls/assignments');
            },
        },
        {
            id: 'minimize',
            icon: (
                <div
                    className={`menu-item-icon minimize-icon${rootStore.ingameStore.sideBarMinimized ? ' minimized' : ''}`}
                />
            ),
            label: null,
            onClick: () => {
                rootStore.ingameStore.sideBarMinimized = !rootStore.ingameStore.sideBarMinimized;
            },
        },
    ];

    return (
        <div
            id="ingame-layout"
            className={`${handle.additionalClass || null}`}
        >
            <div
                className={
                    rootStore.ingameStore.sideBarMinimized ? 'left-side-bar-mini' : 'left-side-bar'
                }
            >
                <div className="logo-outer-container">
                    <Logo
                        showText={!rootStore.ingameStore.sideBarMinimized}
                        width={rootStore.ingameStore.sideBarMinimized ? '70px' : '96px'}
                    />
                </div>
                <div
                    className="menu-container"
                    ref={menuGroupRef}
                    onMouseDown={onMouseDown}
                    onScroll={(e) => {
                        rootStore.ingameStore.leftMenuScrollPosition = (
                            e.nativeEvent.target as HTMLDivElement
                        ).scrollTop;
                    }}
                >
                    {menuEntries.map((me) => (
                        <div
                            key={me.id}
                            className={
                                me.onClick == null
                                    ? me.icon == null
                                        ? 'menu-item-blank'
                                        : 'menu-item-disabled'
                                    : me.icon == null
                                      ? 'menu-item-blank'
                                      : me.active != undefined && me.active(location.pathname)
                                        ? 'menu-item-active'
                                        : 'menu-item'
                            }
                            onClick={me.onClick}
                        >
                            <Tooltip
                                placement="right"
                                title={me.label}
                            >
                                <div className="menu-item-inner">
                                    {me.icon != null && me.icon}
                                    {me.label != null &&
                                        !rootStore.ingameStore.sideBarMinimized && (
                                            <div className="menu-item-label p1">{me.label}</div>
                                        )}
                                </div>
                            </Tooltip>
                        </div>
                    ))}
                </div>
            </div>

            <Suspense>
                <Outlet />
            </Suspense>
        </div>
    );
});
