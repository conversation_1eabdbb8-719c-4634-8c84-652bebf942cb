import { IngameLayout } from '../ingameLayout/ingameContentLayout';
import { observer } from 'mobx-react';
import { ChatPageUseCase, ChatsFullpage } from '@components/chats/chatsFullpage';

import './sessionChats.scss';

const SessionChats = observer((): JSX.Element => {
    return (
        <IngameLayout
            additionalClass="uni-fixed-height"
            showTopBar={true}
            topBarText="Мессенджер"
        >
            <div className="chats-container">
                <ChatsFullpage useCase={ChatPageUseCase.Ingame} />
            </div>
        </IngameLayout>
    );
});

export default SessionChats;
