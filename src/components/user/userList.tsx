import { useReactive } from 'ahooks';
import { TUser } from 'types/user/user';
import { useCallback, useContext, useEffect, useRef, useState } from 'react';
import MainLayout from '@components/ui/layout/mainLayout';
import {
    Button,
    Checkbox,
    Col,
    ConfigProvider,
    Dropdown,
    Form,
    FormInstance,
    Input,
    InputRef,
    List,
    message,
    Modal,
    Pagination,
    Popconfirm,
    Radio,
    Row,
    Select,
    Table,
    TableProps,
    Tooltip,
} from 'antd';
import { Permissions } from '@classes/permissions';
import { useNavigate } from 'react-router-dom';
import { CRMAPIManager } from '@api/crmApiManager';
import { UserListResp } from '@api/responseModels/users/userListResp';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { UserResp } from '@api/responseModels/users/userResp';
import { Common } from '@classes/common';
import { GlobalConstants } from '@classes/constants';
import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFolderOpen } from '@fortawesome/free-solid-svg-icons';
import { TRole } from 'types/user/role';
import { RoleListResp } from '@api/responseModels/roles/roleListResponse';
import { TableRowSelection } from 'antd/es/table/interface';
import _ from 'lodash';
import { ListModeSwitch } from '@components/ui/listModeSwitch/listModeSwitch';
import { Loader } from '@components/ui/loader/loader';
import { TFilter } from 'types/filter';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { FilterDrawer } from '@components/drawers/filterDrawer';
import { TMetadata } from 'types/api/metadata';
import { FilterResp } from '@api/responseModels/filters/filterResponse';
import { SettingsManager } from '@classes/settingsManager';
import { rootStore } from '@store/instanse';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';
import Colors from '@classes/colors';

import './userList.scss';

const EditableContext = React.createContext<FormInstance<any> | null>(null);

type UserDataType = TUser & {
    key: TUser['id'];
    banned: boolean;
    deleted: boolean;
    originalRole: TUser['role'];
    roles: TRole[];
    udtStatus: { color: string; text: string }[];
};

interface EditableRowProps {
    index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form
            form={form}
            component={false}
        >
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

interface EditableCellProps {
    title: React.ReactNode;
    editable: boolean;
    dataIndex: keyof UserDataType;
    record: UserDataType;
    handleSave: (record: UserDataType) => void;
}

const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    ...restProps
}) => {
    const [editing, setEditing] = useState(false);
    const inputRef = useRef<InputRef>(null);
    const selectRef = useRef(null);
    const form = useContext(EditableContext)!;

    useEffect(() => {
        if (editing) {
            inputRef?.current?.focus();
            selectRef?.current?.focus();
        }
    }, [editing]);

    const toggleEdit = () => {
        setEditing(!editing);
        form.setFieldsValue({ [dataIndex]: record[dataIndex] });
    };

    const save = async () => {
        try {
            const values = await form.validateFields();

            toggleEdit();
            handleSave({ ...record, ...values });
        } catch (errInfo) {
            console.log('Save failed:', errInfo);
        }
    };

    let childNode = children;

    if (
        editable &&
        record.role != 'Roles.Admin' &&
        !(record.deleted || record.deleted_at != null)
    ) {
        childNode =
            editing &&
            ((dataIndex == 'role' && record.id != rootStore.currentUserStore.getUser?.id) ||
                dataIndex != 'role') ? (
                <Form.Item
                    style={{ margin: 0 }}
                    name={dataIndex}
                    rules={
                        dataIndex == 'name'
                            ? [
                                  {
                                      required: true,
                                      message: `${title} обязательное поле.`,
                                  },
                                  {
                                      pattern:
                                          /^(?=.*[A-Za-zа-яА-ЯёЁ\d]$)[A-Za-zа-яА-ЯёЁ][A-Za-zа-яА-ЯёЁ\d\s-]{2,32}$/,
                                      message:
                                          'От 3 до 32 символов, кириллица, латиница, цифры, тире и пробелы',
                                  },
                              ]
                            : [
                                  {
                                      required: true,
                                      message: `${title} обязательное поле.`,
                                  },
                              ]
                    }
                >
                    {dataIndex == 'name' && (
                        <Input
                            ref={inputRef}
                            maxLength={32}
                            minLength={2}
                            onPressEnter={save}
                            onBlur={save}
                            placeholder="Введите имя"
                            required
                        />
                    )}
                    {dataIndex == 'role' && (
                        <Select
                            ref={selectRef}
                            onBlur={save}
                            onSelect={save}
                            options={record.roles
                                .filter((r) => r.name != 'Roles.Admin')
                                .map((r) => {
                                    return {
                                        value: r.name,
                                        label: (
                                            <span className="p3">
                                                {Common.makeRoleName(r.name)}
                                            </span>
                                        ),
                                    };
                                })}
                            placeholder="Выберите роль"
                        />
                    )}
                </Form.Item>
            ) : (
                <div
                    className="editable-cell-value-wrap"
                    style={{ paddingInlineEnd: 4 }}
                    onClick={toggleEdit}
                >
                    {children}
                </div>
            );
    }

    return <td {...restProps}>{childNode}</td>;
};

type ColumnTypes = Exclude<TableProps<UserDataType>['columns'], undefined>;

type TUserChange = UserDataType & {
    change_type: 'update' | 'remove' | 'restore';
};

type TState = {
    changes: { [key: TUser['id']]: TUserChange };
    filterPickerOpen: boolean;
    filterPickerTempFilters: TFilter['id'][];
    filterPickerUser: UserDataType | null;
    filters: TFilter[];
    filtersMeta: TMetadata | null;
    groupChatName: string;
    groupNameModalOpen: boolean;
    isLoading: boolean;
    listMode: 'list' | 'table';
    mode: 'current' | 'deleted' | 'all';
    page: number;
    perPage: number;
    role: 'Roles.Admin' | 'Roles.Architect' | 'Roles.Manager' | 'Roles.Client' | 'all';
    roles: TRole[];
    selectedUserRows: React.Key[];
    status: 'current' | 'banned' | 'all';
    users: TUser[];
    usersMeta: TMetadata | null;
};

const UserList = (): JSX.Element => {
    const state = useReactive<TState>({
        changes: {},
        filterPickerOpen: false,
        filterPickerTempFilters: [],
        filterPickerUser: null,
        filters: [],
        filtersMeta: null,
        isLoading: false,
        groupChatName: '',
        groupNameModalOpen: false,
        listMode: 'list',
        mode: 'current',
        page: 1,
        perPage: 10,
        role: 'all',
        roles: [],
        selectedUserRows: [],
        status: 'current',
        users: [],
        usersMeta: null,
    });
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const chatNameMask =
        /^(?=.*[A-Za-zа-яА-ЯёЁ0-9]$)[A-Za-zа-яА-ЯёЁ][A-Za-zа-яА-ЯёЁ\d\s,.-]{3,24}$/;
    const userTableEditable =
        Permissions.checkPermission(Permissions.RoleList) &&
        Permissions.checkPermission(Permissions.UserList) &&
        Permissions.checkPermission(Permissions.UserGet) &&
        Permissions.checkPermission(Permissions.UserUpdate) &&
        Permissions.checkPermission(Permissions.UserRestore) &&
        Permissions.checkPermission(Permissions.UserDelete) &&
        Permissions.checkPermission(Permissions.UserBan) &&
        Permissions.checkPermission(Permissions.UserUnban) &&
        Permissions.checkPermission(Permissions.UserChangeRole);
    const filtersEditable =
        Permissions.checkPermission(Permissions.FilterList) &&
        Permissions.checkPermission(Permissions.FilterGet) &&
        Permissions.checkPermission(Permissions.FilterCreate) &&
        Permissions.checkPermission(Permissions.FilterUpdate) &&
        Permissions.checkPermission(Permissions.FilterDelete) &&
        Permissions.checkPermission(Permissions.FilterRestore) &&
        Permissions.checkPermission(Permissions.FilterBulkAdd) &&
        Permissions.checkPermission(Permissions.FilterBulkResult);
    const anyChanges = Object.keys(state.changes).length > 0;
    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };
    const tableStatuses = {
        ban: GlobalConstants.TableStatuses['ban'],
        deleted: GlobalConstants.TableStatuses['deleted'],
        restored: GlobalConstants.TableStatuses['restored'],
        'role-change': GlobalConstants.TableStatuses['role-change'],
        unban: GlobalConstants.TableStatuses['unban'],
        unfilled: GlobalConstants.TableStatuses['unfilled'],
        updated: GlobalConstants.TableStatuses['updated'],
    };
    const defaultColumns: (ColumnTypes[number] & { editable?: boolean; dataIndex: string })[] = [
        {
            title: 'ID пользователя',
            dataIndex: 'id',
            render: (value) => (
                <span className="table-id p3">
                    {value}
                    <Row>
                        <CopyButton
                            size={20}
                            textToCopy={`Пользователь ${value}`}
                            textToShow={'ID пользователя скопирован'}
                        />
                        <Tooltip
                            title={anyChanges ? 'Сохраните изменения для перехода' : 'Перейти'}
                        >
                            <Button
                                className="open-btn"
                                disabled={anyChanges || state.mode == 'deleted'}
                                icon={<FontAwesomeIcon icon={faFolderOpen} />}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    if (!(anyChanges || state.mode == 'deleted')) {
                                        navigate(`/management/users/${value}`);
                                    }
                                }}
                                type="text"
                            />
                        </Tooltip>
                    </Row>
                </span>
            ),
            sorter: (a, b) => a.id.localeCompare(b.id),
            width: 200,
        },
        {
            title: 'Имя',
            dataIndex: 'name',
            editable: userTableEditable,
            render: (_, rec) => {
                const getUserName = () =>
                    Common.isNullOrEmptyString(rec.name) ? `-Без имени` : rec.name;
                return (
                    <Row className="table-user">
                        <Col className="user-avatar">
                            {rec.picture == null && (
                                <div className="p1-strong">{getUserName()[0]}</div>
                            )}
                        </Col>
                        <Col className="p3 user-name">{getUserName()}</Col>
                    </Row>
                );
            },
            sorter: (a, b) => {
                const aEmpty = Common.isNullOrEmptyString(a.name);
                const bEmpty = Common.isNullOrEmptyString(b.name);
                if (aEmpty && bEmpty) {
                    return 0;
                } else if (aEmpty && !bEmpty) {
                    return 1;
                } else if (!aEmpty && bEmpty) {
                    return -1;
                } else {
                    return a.name.localeCompare(b.name);
                }
            },
            width: 200,
        },
        {
            title: 'Состояние',
            dataIndex: 'udtStatus',
            filters: Object.keys(tableStatuses).map((tsk) => {
                const tsv = tableStatuses[tsk];
                return {
                    text: (
                        <FilterButton
                            key={tsk}
                            hex={tsv.color}
                            text={tsv.text}
                        />
                    ),
                    value: tsv.text,
                };
            }),
            onFilter: (value, record) => {
                return record.udtStatus.find((si) => si.text.includes('' + value)) != undefined;
            },
            render: (_, rec) => (
                <Row className="table-status">
                    {rec.udtStatus.map((si, i) => (
                        <FilterButton
                            key={`status-${i}`}
                            hex={si.color}
                            text={si.text}
                        />
                    ))}
                    {rec.udtStatus.length == 0 && <span className="p3 gray">Нет статусов</span>}
                </Row>
            ),
            width: 200,
        },
        {
            title: 'Роль',
            dataIndex: 'role',
            editable: userTableEditable,
            render: (value) => <span className="p3">{value}</span>,
            width: 200,
        },
        {
            title: 'Фильтры',
            dataIndex: 'filters',
            filters: state.filters.map((fi) => {
                return {
                    text: (
                        <FilterButton
                            key={fi.id}
                            hex={fi.color_hex}
                            text={fi.name}
                        />
                    ),
                    value: fi.name,
                };
            }),
            onFilter: (value, record) => {
                return (
                    record.filters.find((filter) => filter.name.includes('' + value)) != undefined
                );
            },
            render: (_, record) => (
                <Row
                    className="filter-row"
                    onClick={() => {
                        if (userTableEditable && filtersEditable && record.role != 'Roles.Admin') {
                            state.filterPickerUser = { ...record };
                            setFilterPickerOpen(true);
                        }
                    }}
                >
                    {record.filters.slice(0, 3).map((fi) => (
                        <FilterButton
                            key={`${record.id}-${fi.id}`}
                            hex={fi.color_hex}
                            text={fi.name}
                        />
                    ))}
                    {record.filters.length > 3 && (
                        <Dropdown
                            menu={{
                                items: record.filters.slice(3, record.filters.length).map((f) => {
                                    return {
                                        key: f.id,
                                        label: (
                                            <FilterButton
                                                key={`filter-${record.id}-${f.id}`}
                                                hex={f.color_hex}
                                                text={f.name}
                                            />
                                        ),
                                    };
                                }),
                            }}
                            trigger={['hover', 'click']}
                        >
                            <Row>
                                <FilterButton
                                    key="more-filters"
                                    hex={Colors.Neutral[50]}
                                    text={`+${record.filters.length - 3}`}
                                />
                            </Row>
                        </Dropdown>
                    )}
                    {record.filters.length == 0 && <span className="p3">Нет фильтров</span>}
                </Row>
            ),
            width: 250,
        },
        {
            title: 'Пригласил(а)',
            dataIndex: 'invite_sender_id',
            filters: _.uniq(
                state.users.map((i) => {
                    const isSystem = i.invite_sender_id == null || i.invite_sender_id == 'None';
                    return {
                        text: (
                            <span className="p3">
                                {isSystem ? 'Система' : i.invite_sender_name}
                            </span>
                        ),
                        value: i.invite_sender_id,
                    };
                }),
            ),
            onFilter: (value, record) => {
                return record.invite_sender_id == value;
            },
            render: (_, rec) => {
                const getUserName = () =>
                    rec.invite_sender_id == null || rec.invite_sender_id == 'None'
                        ? `Система`
                        : rec.invite_sender_name;
                return (
                    <Row className="table-user">
                        <Col className="user-avatar">
                            {rec.invite_sender_picture == null && (
                                <div className="p1-strong">{getUserName()[0]}</div>
                            )}
                        </Col>
                        <Col className="p3 user-name">{getUserName()}</Col>
                    </Row>
                );
            },
            sorter: (a, b) => {
                const aEmpty = Common.isNullOrEmptyString(a.invite_sender_name);
                const bEmpty = Common.isNullOrEmptyString(b.invite_sender_name);
                if (aEmpty && bEmpty) {
                    return 0;
                } else if (aEmpty && !bEmpty) {
                    return 1;
                } else if (!aEmpty && bEmpty) {
                    return -1;
                } else {
                    return a.name.localeCompare(b.name);
                }
            },
            width: 200,
        },
        {
            title: 'Логин',
            dataIndex: 'login',
            render: (value) => (
                <Row>
                    <span className="p3">{value}</span>
                    <CopyButton
                        size={20}
                        textToCopy={value}
                        textToShow={'Логин скопирован'}
                    />
                </Row>
            ),
            width: 200,
        },
        {
            title: 'Email',
            dataIndex: 'email',
            render: (value) => (
                <Row>
                    <span className="p3">{value}</span>
                    <CopyButton
                        size={20}
                        textToCopy={value}
                        textToShow={'Email скопирован'}
                    />
                </Row>
            ),
            width: 250,
        },
        {
            title: 'Создан',
            dataIndex: 'created_at',
            render: (value) => (
                <span className="p3 lighter-tone">{Common.formatDateString(value)}</span>
            ),
            sorter: (a, b) => {
                const aTime = new Date(a.created_at).getTime();
                const bTime = new Date(b.created_at).getTime();
                return aTime - bTime;
            },
            width: 200,
        },
        {
            title: 'Изменён',
            dataIndex: 'updated_at',
            render: (value) => (
                <span className="p3 lighter-tone">{Common.formatDateString(value)}</span>
            ),
            sorter: (a, b) => {
                const aTime = new Date(a.updated_at).getTime();
                const bTime = new Date(b.updated_at).getTime();
                return aTime - bTime;
            },
            width: 200,
        },
    ];

    function updateTableUser(record: UserDataType) {
        if (Object.keys(state.changes).includes(record.id)) {
            const originalUser = state.users.find((user) => user.id == record.id);
            if (
                originalUser != undefined &&
                state.changes[record.id].change_type != 'remove' &&
                _.isEqual(
                    {
                        ...record,
                        filters: [
                            ...record.filters.map((f) => {
                                return {
                                    color_hex: f.color_hex,
                                    id: f.id,
                                    is_protected: f.is_protected,
                                    name: f.name,
                                    target: f.target,
                                };
                            }),
                        ],
                        permissions: { ...record.permissions },
                        roles: [...record.roles],
                        change_type: 'update',
                    },
                    {
                        ...originalUser,
                        filters: [...originalUser.filters],
                        permissions: { ...originalUser.permissions },
                        roles: [...state.roles],
                        change_type: 'update',
                        key: record.key,
                        banned: record.banned,
                        deleted: record.deleted,
                        originalRole: record.originalRole,
                        udtStatus: record.udtStatus,
                    },
                )
            ) {
                delete state.changes[record.id];
            } else {
                state.changes[record.id] = {
                    ...state.changes[record.id],
                    ...record,
                };
            }
        } else {
            const originalUser = state.users.find((user) => user.id == record.id);
            if (
                !_.isEqual(record, {
                    ...originalUser,
                    key: originalUser.id,
                    banned: false,
                    deleted: false,
                    originalRole: originalUser.role,
                    roles: state.roles,
                    udtStatus: [],
                })
            ) {
                state.changes[record.id] = {
                    ...record,
                    change_type: 'update',
                };
            }
        }
    }

    const columns = defaultColumns.map((col) => {
        if (!col.editable) {
            return col;
        }
        return {
            ...col,
            onCell: (record: UserDataType) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave: updateTableUser,
            }),
        };
    });

    async function loadUserList(page: number = state.page, addition: boolean = false) {
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<UserListResp>(async (api) => {
                return await api.getUserList({
                    query: null,
                    role: state.listMode == 'list' && state.role != 'all' ? state.role : null,
                    page: page,
                    per_page: state.perPage,
                });
            });
            if (result.errorMessages) throw result.errorMessages;
            let ul = result.data.data;
            if (state.listMode == 'list') {
                switch (state.mode) {
                    case 'current': {
                        ul = ul.filter((uli) => uli.deleted_at == null);
                        break;
                    }
                    case 'deleted': {
                        ul = ul.filter((uli) => uli.deleted_at != null);
                        break;
                    }
                }
            }
            state.users = addition ? [...state.users, ...ul] : ul;
            state.usersMeta = result.data.meta;
        } catch (err) {
            messageApi.error('Ошибка при получении списка пользователей');
            console.log(err);
        }
        state.isLoading = false;
    }

    async function loadRoles() {
        state.isLoading = true;
        try {
            const roles = await CRMAPIManager.request<RoleListResp>(async (api) => {
                return await api.getRoleList();
            });
            if (roles.errorMessages) throw roles.errorMessages;
            state.roles = roles.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при получении списка ролей');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadFilters() {
        state.isLoading = true;
        try {
            const filters = await CRMAPIManager.getAll<FilterListResp>(
                async (api, page, per_page) => {
                    return await api.getFilterList({
                        query: null,
                        page: page,
                        per_page: per_page,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            deleted: 'null',
                        },
                    });
                },
            );
            if (filters.errorMessages) throw filters.errorMessages;
            state.filters = filters.data.data;
            state.filtersMeta = filters.data.meta;
        } catch (err) {
            messageApi.error('Ошибка при получении списка фильтров');
            console.log(err);
        }
        state.isLoading = false;
    }

    async function loadNecessary() {
        if (Permissions.checkPermission(Permissions.RoleList)) {
            await loadRoles();
        }
        state.changes = {};
        state.mode = 'current';
        state.role = 'all';
        state.status = 'all';
        state.selectedUserRows = [];
        await loadFilters();
        await loadUserList();
    }

    useEffect(() => {
        loadNecessary();
    }, []);

    useEffect(() => {
        state.page = 1;
        loadUserList();
    }, [
        state.mode,
        state.role,
        state.status,
    ]);

    function makeListItems() {
        const arr = [];
        if (Permissions.checkPermission(Permissions.UserCreate) && state.mode == 'current') {
            arr.push('add');
        }
        arr.push(...state.users);
        return arr;
    }

    async function listModeChange(value: TState['listMode']) {
        if (value == 'table') {
            state.mode = 'current';
            state.role = 'all';
            state.status = 'all';
        }
        state.listMode = value;
    }

    function getStatusList(user: TUser) {
        const statuses: UserDataType['udtStatus'] = [];
        const change = state.changes[user.id];
        if (change != undefined && change.change_type == 'remove') {
            statuses.push(tableStatuses['deleted']);
        }
        if (change != undefined && change.change_type == 'restore') {
            statuses.push(tableStatuses['restored']);
        }
        if (change != undefined && user.is_banned == false && change.banned == true) {
            statuses.push(tableStatuses['ban']);
        }
        if (change != undefined && user.is_banned == true && change.banned == false) {
            statuses.push(tableStatuses['unban']);
        }
        if (change != undefined && change.role != change.originalRole) {
            statuses.push(tableStatuses['role-change']);
        }
        if (change != undefined && change.change_type == 'update') {
            statuses.push(tableStatuses['updated']);
        }
        if (user.name.trim().length == 0 || user.role == null) {
            statuses.push(tableStatuses['unfilled']);
        }
        return statuses;
    }

    function makeTableData(): UserDataType[] {
        const tableData = state.users.map((user) => {
            const change = state.changes[user.id];
            const tempUser = change != undefined ? change : user;
            const banned = change == undefined ? user.is_banned : change.banned;
            const deleted =
                change == undefined ? user.deleted_at != null : change.change_type == 'remove';
            return {
                ...tempUser,
                key: tempUser.id,
                banned: banned,
                deleted: deleted,
                originalRole: user.role,
                roles: state.roles,
                udtStatus: getStatusList(tempUser),
            };
        });
        return tableData.filter((tdi) => (state.mode == 'current' ? !tdi.deleted : tdi.deleted));
    }

    const userRowSelection: TableRowSelection<UserDataType> = {
        columnWidth: 40,
        getCheckboxProps: (user: UserDataType) => ({
            disabled: !userTableEditable || user.role == 'Roles.Admin',
            name: user.id,
        }),
        onChange: (srk) => (state.selectedUserRows = srk),
        selectedRowKeys: state.selectedUserRows,
    };

    function makeChangeStats() {
        const values = Object.values(state.changes);
        let chDelete = 0;
        let chEdit = 0;
        let chRestore = 0;
        for (let i = 0; i < values.length; i++) {
            switch (values[i].change_type) {
                case 'remove': {
                    chDelete += 1;
                    break;
                }
                case 'update': {
                    chEdit += 1;
                    break;
                }
                case 'restore': {
                    chRestore += 1;
                    break;
                }
            }
        }
        return `(изменены: ${chEdit}, удалены: ${chDelete}, восстановлены: ${chRestore})`;
    }

    function deleteSelected() {
        const tempChanges = { ...state.changes };
        const tempSKR = [...state.selectedUserRows];
        for (let i = 0; i < tempSKR.length; i++) {
            const change = Object.keys(tempChanges).find((k) => k == tempSKR[i]);
            if (change != undefined) {
                if (tempChanges[change].change_type == 'restore') {
                    delete tempChanges[change];
                } else {
                    tempChanges[change].change_type = 'remove';
                }
            } else {
                const user = state.users.find((user) => user.id == tempSKR[i]);
                tempChanges['' + tempSKR[i]] = {
                    ...user,
                    key: user.id,
                    banned: user.is_banned,
                    change_type: 'remove',
                    deleted: true,
                    originalRole: user.role,
                    roles: state.roles,
                    udtStatus: [],
                };
            }
        }
        state.changes = tempChanges;
        state.selectedUserRows = [];
    }

    function restoreSelected() {
        const tempChanges = { ...state.changes };
        const tempSKR = [...state.selectedUserRows];
        for (let i = 0; i < tempSKR.length; i++) {
            const change = Object.keys(tempChanges).find((k) => k == tempSKR[i]);
            if (change != undefined) {
                delete tempChanges[change];
            } else {
                const user = state.users.find((user) => user.id == tempSKR[i]);
                tempChanges['' + tempSKR[i]] = {
                    ...user,
                    key: user.id,
                    banned: user.is_banned,
                    change_type: 'restore',
                    deleted: false,
                    originalRole: user.role,
                    roles: state.roles,
                    udtStatus: [],
                };
            }
        }
        state.changes = tempChanges;
        state.selectedUserRows = [];
    }

    function banSelected() {
        const tempChanges = { ...state.changes };
        const tempSKR = [...state.selectedUserRows];
        for (let i = 0; i < tempSKR.length; i++) {
            const change = Object.keys(tempChanges).find((k) => k == tempSKR[i]);
            if (change != undefined) {
                delete tempChanges[change];
            } else {
                const user = state.users.find((user) => user.id == tempSKR[i]);
                if (user.is_banned == false) {
                    tempChanges['' + tempSKR[i]] = {
                        ...user,
                        key: user.id,
                        banned: true,
                        change_type: 'update',
                        deleted: false,
                        originalRole: user.role,
                        roles: state.roles,
                        udtStatus: [],
                    };
                }
            }
        }
        state.changes = tempChanges;
        state.selectedUserRows = [];
    }

    function unbanSelected() {
        const tempChanges = { ...state.changes };
        const tempSKR = [...state.selectedUserRows];
        for (let i = 0; i < tempSKR.length; i++) {
            const change = Object.keys(tempChanges).find((k) => k == tempSKR[i]);
            if (change != undefined) {
                delete tempChanges[change];
            } else {
                const user = state.users.find((user) => user.id == tempSKR[i]);
                if (user.is_banned == true) {
                    tempChanges['' + tempSKR[i]] = {
                        ...user,
                        key: user.id,
                        banned: false,
                        change_type: 'update',
                        deleted: false,
                        originalRole: user.role,
                        roles: state.roles,
                        udtStatus: [],
                    };
                }
            }
        }
        state.changes = tempChanges;
        state.selectedUserRows = [];
    }

    const countUnfinishedUsers = useCallback(() => {
        const keys = Object.keys(state.changes);
        let count = 0;
        for (let i = 0; i < keys.length; i++) {
            const value = state.changes[keys[i]];
            if (
                value.change_type != 'remove' &&
                (value.name.trim().length == 0 || value.role == null)
            ) {
                count++;
            }
        }
        return count;
    }, [state.changes]);

    async function saveChanges() {
        const counter = countUnfinishedUsers();
        if (counter > 0) {
            messageApi.warning(`Остались не заполненные пользователи: ${counter} шт.`);
            return false;
        }
        let returnResult = false;
        try {
            const changes = Object.values(state.changes);
            for (let i = 0; i < changes.length; i++) {
                const change = changes[i];
                if (change.role != change.originalRole) {
                    const result = await CRMAPIManager.request<UserResp>(async (api) => {
                        return await api.changeUserRole(change.id, change.role);
                    });
                    if (result.errorMessages) throw result.errorMessages;
                }
                const user = state.users.find((u) => u.id == change.id);
                if (change.banned != user.is_banned) {
                    const result = await CRMAPIManager.request<UserResp>(async (api) => {
                        if (change.banned) {
                            return await api.banUser(user.id);
                        } else {
                            return await api.unbanUser(user.id);
                        }
                    });
                    if (result.errorMessages) throw result.errorMessages;
                }
                const result = await CRMAPIManager.request<UserResp>(async (api) => {
                    if (change.change_type == 'update') {
                        return await api.updateUser({
                            id: change.id,
                            description: change.description,
                            email: change.email,
                            filters: change.filters.map((f) => {
                                return { ...f, target: 'users' };
                            }),
                            invite_sender_id: change.invite_sender_id,
                            invite_sender_name: change.invite_sender_name,
                            invite_sender_picture: change.invite_sender_picture,
                            is_banned: change.is_banned,
                            is_verified: change.is_verified,
                            login: change.login,
                            name: change.name,
                            picture: change.picture,
                            permissions: change.permissions,
                            role: change.role,
                            status: change.status,
                            created_at: change.created_at,
                            updated_at: change.updated_at,
                            deleted_at: change.deleted_at,
                        });
                    } else if (change.change_type == 'remove') {
                        return await api.deleteUser(change.id);
                    } else {
                        return await api.restoreUser(change.id);
                    }
                });
                if (result.errorMessages) throw result.errorMessages;
            }
            message.success('Изменения сохранены');
            await loadNecessary();
            returnResult = true;
        } catch (errors) {
            message.error('Ошибка при сохранении, попробуйте ещё раз');
            console.log(errors);
        }
        state.isLoading = false;
        return returnResult;
    }

    async function restoreUser(user: TUser) {
        messageApi.open({
            type: 'loading',
            content: 'Восстанавливаем...',
            duration: 0,
        });
        try {
            const result = await CRMAPIManager.request<UserResp>(async (api) => {
                return await api.restoreUser(user.id);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadUserList();
            messageApi.destroy();
            messageApi.success('Пользователь восстановлен');
        } catch (errors) {
            messageApi.destroy();
            messageApi.error('Ошибка :(');
            console.log(errors);
        }
    }

    function updateSelUserFilters() {
        updateTableUser({
            ...state.filterPickerUser,
            filters:
                state.filterPickerTempFilters.length > 0
                    ? state.filterPickerTempFilters.map((tfi) =>
                          state.filters.find((fi) => fi.id == tfi),
                      )
                    : [],
        });
        state.filterPickerOpen = false;
        state.filterPickerTempFilters = [];
        state.filterPickerUser = null;
    }

    async function onFilterAdd(filter: TFilter) {
        if (!filtersEditable) {
            messageApi.error('Запрещена работа с фильтрами');
            return;
        }
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<FilterResp>(async (api) => {
                return await api.createFilter(filter);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadFilters();
        } catch (errors) {
            messageApi.error('Ошибка при создании фильтра');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function updateSelUserTempFilters(filter: TFilter) {
        if (state.filterPickerTempFilters.includes(filter.id)) {
            state.filterPickerTempFilters = state.filterPickerTempFilters.filter(
                (tfi) => tfi != filter.id,
            );
        } else {
            state.filterPickerTempFilters = [
                ...state.filterPickerTempFilters,
                filter.id,
            ];
        }
    }

    function setFilterPickerOpen(isOpen: boolean) {
        if (isOpen) {
            state.filterPickerTempFilters = state.filterPickerUser.filters.map((fi) => fi.id);
            state.filterPickerOpen = true;
        } else {
            state.filterPickerOpen = false;
            state.filterPickerTempFilters = [];
            state.filterPickerUser = null;
        }
    }

    function allowPrivateChat(user_id: TUser['id']) {
        const creds = SettingsManager.getConnectionCredentials();
        return (
            Permissions.checkPermission(Permissions.ChatCreate) &&
            state.mode == 'current' &&
            user_id != creds.user_id &&
            (state.listMode == 'list' || (state.listMode == 'table' && !anyChanges))
        );
    }

    async function chatWithUser(user: TUser) {
        if (!allowPrivateChat(user.id)) {
            messageApi.warning('Чат с пользователем не разрешён');
            return;
        }
        state.isLoading = true;
        try {
            const result = await rootStore.socketStore.findOrCreatePrivateChat(
                rootStore.currentUserStore.getUser,
                user,
            );
            if (result.success) {
                navigate(`/chats/${result.chat_id}`);
            }
        } catch (errors) {
            messageApi.error('Ошибка при поиске/создании чата с пользователем');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function chatWithSelectedUser() {
        if (state.selectedUserRows.length != 1) {
            message.warning('Должен быть выбран 1 пользователь');
            return;
        }
        const user_id = state.selectedUserRows[0].toString();
        const user = state.users.find((u) => u.id == user_id);
        if (user == undefined) {
            message.warning('Не удалось найти пользователя');
            return;
        }
        await chatWithUser(user);
    }

    function allowGroupChat() {
        const creds = SettingsManager.getConnectionCredentials();
        return (
            Permissions.checkPermission(Permissions.ChatCreate) &&
            state.mode != 'deleted' &&
            !anyChanges &&
            state.selectedUserRows.length <=
                (state.selectedUserRows.find((srk) => srk == creds.user_id) == undefined ? 49 : 50)
        );
    }

    function prepareChatWithSelectedUserList() {
        if (!allowGroupChat()) {
            message.warning(
                'Без несохранённых изменений, должно быть выбрано от 2 до 50 не удалённых пользователей',
            );
            return;
        }
        state.groupChatName = '';
        state.groupNameModalOpen = true;
    }

    async function onChatNameModalConfirm() {
        if (!chatNameMask.test(state.groupChatName)) {
            messageApi.warning('Некорректное название чата');
            return;
        }
        state.isLoading = true;
        try {
            const result = await rootStore.socketStore.findOrCreateGroupChat(
                rootStore.currentUserStore.getUser,
                state.groupChatName,
                state.selectedUserRows.map((srk) => srk.toString()),
            );
            if (result.success) {
                navigate(`/chats/${result.chat_id}`);
            }
        } catch (errors) {
            messageApi.error('Ошибка при поиске/создании группового чата');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function onPageOrPerPageChange(page: number, pageSize: number) {
        if (page != state.page) {
            await loadUserList(page);
            state.page = page;
        }
        if (pageSize != state.perPage) {
            if (state.usersMeta?.total == null || state.usersMeta?.total == 0) return;
            state.perPage = pageSize;
            state.page = Math.ceil(state.usersMeta?.from_ / pageSize);
            await loadUserList();
        }
    }

    async function loadMore() {
        state.page += 1;
        await loadUserList(state.page, true);
    }

    return (
        <MainLayout
            activeTab="users"
            additionalClass="list-min-width"
            tabSet="management"
        >
            <div className="user-list-container">
                {contextHolder}
                <PreventLeaving
                    anyChanges={anyChanges}
                    onSave={saveChanges}
                />
                {state.filtersMeta != null && (
                    <FilterDrawer
                        filterList={state.filters.filter((f) => !f.is_protected)}
                        filterListMeta={state.filtersMeta}
                        fixedTarget="users"
                        isOpen={state.filterPickerOpen}
                        onConfirm={updateSelUserFilters}
                        onFilterAdd={onFilterAdd}
                        onSelect={updateSelUserTempFilters}
                        selected={state.filterPickerTempFilters}
                        setIsOpen={setFilterPickerOpen}
                    />
                )}
                {state.groupNameModalOpen && (
                    <Modal
                        cancelText="Отмена"
                        okButtonProps={{
                            disabled: !chatNameMask.test(state.groupChatName),
                        }}
                        okText="Создать"
                        onCancel={() => (state.groupNameModalOpen = false)}
                        onOk={() => onChatNameModalConfirm()}
                        open={state.groupNameModalOpen}
                        title={
                            <Row className="modal-header">
                                <Col>
                                    <h6>Введите название чата</h6>
                                </Col>
                            </Row>
                        }
                        wrapClassName="chat-name-modal"
                    >
                        <Col>
                            <Row>
                                <span className="p3">Название чата:</span>
                            </Row>
                            <Row>
                                <Tooltip title="От 3 до 24 символов, буквы, цифры, пробел, точка, запятая, тире">
                                    <Input
                                        allowClear
                                        autoComplete="off"
                                        maxLength={24}
                                        minLength={3}
                                        onChange={(e) => {
                                            state.groupChatName = e.target.value;
                                        }}
                                        placeholder="Введите название чата"
                                        showCount
                                        status={
                                            chatNameMask.test(state.groupChatName) ? null : 'error'
                                        }
                                        value={state.groupChatName}
                                    />
                                </Tooltip>
                            </Row>
                        </Col>
                    </Modal>
                )}
                <div className="radio-group">
                    <Popconfirm
                        cancelText="Отмена"
                        disabled={state.listMode == 'list'}
                        okText="ОК"
                        onConfirm={() => {
                            listModeChange('list');
                        }}
                        title="Не сохранённые изменения будут потеряны"
                    >
                        <ListModeSwitch
                            onChange={listModeChange}
                            value={state.listMode}
                        />
                    </Popconfirm>
                    {state.listMode == 'list' && (
                        <Radio.Group
                            onChange={(e) => {
                                e.stopPropagation();
                                state.mode = e.target.value;
                            }}
                            options={[
                                { label: 'Текущие', value: 'current' },
                                { label: 'Корзина', value: 'deleted' },
                                { label: 'Все', value: 'all' },
                            ]}
                            optionType="button"
                            value={state.mode}
                        />
                    )}
                    {state.listMode == 'list' && (
                        <Radio.Group
                            onChange={(e) => {
                                e.stopPropagation();
                                state.role = e.target.value;
                            }}
                            options={[
                                { label: 'Админ', value: 'Roles.Admin' },
                                { label: 'Архитектор', value: 'Roles.Architect' },
                                { label: 'Менеджер', value: 'Roles.Manager' },
                                { label: 'Клиент', value: 'Roles.Client' },
                                { label: 'Все', value: 'all' },
                            ]}
                            optionType="button"
                            value={state.role}
                        />
                    )}
                </div>
                {state.listMode == 'list' && (
                    <div className="user-list">
                        <ConfigProvider
                            theme={{
                                token: GlobalConstants.ListGridSettings,
                            }}
                        >
                            <List
                                dataSource={makeListItems()}
                                grid={GlobalConstants.ListGridCols}
                                loading={state.isLoading}
                                locale={{
                                    emptyText: (
                                        <Col
                                            className="empty-text p3"
                                            flex={1}
                                        >
                                            <Row>Таких пользователей нет :)</Row>
                                        </Col>
                                    ),
                                }}
                                renderItem={(item: 'add' | TUser) => {
                                    if (item == 'add') {
                                        return (
                                            <div
                                                className="user-list-add"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    navigate('/management/invitations/new');
                                                }}
                                            >
                                                <div className="add-icon" />
                                            </div>
                                        );
                                    } else {
                                        return (
                                            <div className="user-list-card">
                                                <Row>
                                                    <Col>
                                                        <div
                                                            className={
                                                                item.role == 'Roles.Client'
                                                                    ? 'handsome-client'
                                                                    : 'handsome-staff'
                                                            }
                                                        />
                                                    </Col>
                                                    <Col flex={1}>
                                                        <Row className="header-row">
                                                            <Col flex={1}>
                                                                <Row className="user-name">
                                                                    <div className="p2-strong cut-name">
                                                                        {item.name}
                                                                    </div>
                                                                    <CopyButton
                                                                        textToCopy={item.name}
                                                                        textToShow="Имя скопировано"
                                                                        size={24}
                                                                    />
                                                                </Row>
                                                                <Row className="user-category">
                                                                    <div className="p2">
                                                                        {Common.makeRoleName(
                                                                            item.role,
                                                                        )}
                                                                    </div>
                                                                </Row>
                                                            </Col>
                                                        </Row>
                                                        <Row className="body-row">
                                                            {item.filters
                                                                .slice(0, 3)
                                                                .map((filter, i) => {
                                                                    return (
                                                                        <FilterButton
                                                                            key={`user-${item.id}-${i}`}
                                                                            hex={filter.color_hex}
                                                                            text={filter.name}
                                                                        />
                                                                    );
                                                                })}
                                                            {item.filters.length > 3 && (
                                                                <Dropdown
                                                                    menu={{
                                                                        items: item.filters
                                                                            .slice(
                                                                                3,
                                                                                item.filters.length,
                                                                            )
                                                                            .map((f) => {
                                                                                return {
                                                                                    key: f.id,
                                                                                    label: (
                                                                                        <FilterButton
                                                                                            key={`user-filter-${f.name}`}
                                                                                            hex={
                                                                                                f.color_hex
                                                                                            }
                                                                                            text={
                                                                                                f.name
                                                                                            }
                                                                                        />
                                                                                    ),
                                                                                };
                                                                            }),
                                                                    }}
                                                                    trigger={['hover', 'click']}
                                                                >
                                                                    <Row>
                                                                        <FilterButton
                                                                            key="more-filters"
                                                                            hex={Colors.Neutral[50]}
                                                                            text={`+${item.filters.length - 3}`}
                                                                        />
                                                                    </Row>
                                                                </Dropdown>
                                                            )}
                                                            {item.filters.length == 0 && (
                                                                <span className="p3">
                                                                    Нет фильтров
                                                                </span>
                                                            )}
                                                        </Row>
                                                    </Col>
                                                </Row>
                                                <Row className="event-card-controls p3">
                                                    <Button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            navigate(
                                                                `/management/users/${item.id}`,
                                                            );
                                                        }}
                                                    >
                                                        Открыть
                                                    </Button>
                                                    {item.deleted_at != null && (
                                                        <Button
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                restoreUser(item);
                                                            }}
                                                        >
                                                            Восстановить
                                                        </Button>
                                                    )}
                                                    {allowPrivateChat(item.id) && (
                                                        <Button
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                chatWithUser(item);
                                                            }}
                                                        >
                                                            Чат
                                                        </Button>
                                                    )}
                                                </Row>
                                            </div>
                                        );
                                    }
                                }}
                            />
                        </ConfigProvider>
                        <Row className="pagination-row">
                            {state.usersMeta?.total != null &&
                                state.page != state.usersMeta?.last_page && (
                                    <Button
                                        className="p3"
                                        onClick={loadMore}
                                        type="primary"
                                    >
                                        Показать ещё
                                    </Button>
                                )}
                            <Pagination
                                align="end"
                                current={state.page}
                                pageSize={state.perPage}
                                pageSizeOptions={[
                                    10,
                                    20,
                                    50,
                                    100,
                                ]}
                                total={state.usersMeta?.total ?? 0}
                                onChange={onPageOrPerPageChange}
                                showSizeChanger
                            />
                        </Row>
                    </div>
                )}
                {state.listMode == 'table' && (
                    <Row className="user-table">
                        {state.isLoading && <Loader />}
                        <Col>
                            <Row className="header-row">
                                <Col>
                                    <h4>
                                        {`Пользователей: ${
                                            state.users.length
                                        } ${makeChangeStats()}`}
                                    </h4>
                                </Col>
                            </Row>
                            <Row className="body-row">
                                <Col>
                                    <Row className="table-actions">
                                        <Col>
                                            <span className="selected p3">
                                                {state.selectedUserRows.length} выбрано
                                            </span>
                                        </Col>
                                        {state.selectedUserRows.length == 1 &&
                                            allowPrivateChat(
                                                state.selectedUserRows[0].toString(),
                                            ) && (
                                                <Button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        chatWithSelectedUser();
                                                    }}
                                                >
                                                    Чат
                                                </Button>
                                            )}
                                        {state.selectedUserRows.length > 1 && allowGroupChat() && (
                                            <Button
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    prepareChatWithSelectedUserList();
                                                }}
                                            >
                                                Чат
                                            </Button>
                                        )}
                                        {state.selectedUserRows.length > 0 &&
                                            state.mode == 'current' && (
                                                <Col>
                                                    <Button
                                                        danger
                                                        disabled={!userTableEditable}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            banSelected();
                                                        }}
                                                    >
                                                        Бан
                                                    </Button>
                                                </Col>
                                            )}
                                        {state.selectedUserRows.length > 0 &&
                                            state.mode == 'current' && (
                                                <Col>
                                                    <Button
                                                        disabled={!userTableEditable}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            unbanSelected();
                                                        }}
                                                    >
                                                        Разбан
                                                    </Button>
                                                </Col>
                                            )}
                                        {state.selectedUserRows.length > 0 &&
                                            state.mode == 'current' && (
                                                <Col>
                                                    <Button
                                                        danger
                                                        disabled={!userTableEditable}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            deleteSelected();
                                                        }}
                                                    >
                                                        Удалить
                                                    </Button>
                                                </Col>
                                            )}
                                        {state.selectedUserRows.length > 0 &&
                                            state.mode == 'deleted' && (
                                                <Col>
                                                    <Button
                                                        danger
                                                        disabled={!userTableEditable}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            restoreSelected();
                                                        }}
                                                    >
                                                        Восстановить
                                                    </Button>
                                                </Col>
                                            )}
                                        <Col>
                                            <Checkbox
                                                checked={state.mode == 'deleted'}
                                                onClick={() => {
                                                    state.selectedUserRows = [];
                                                    state.mode =
                                                        state.mode == 'current'
                                                            ? 'deleted'
                                                            : 'current';
                                                }}
                                            >
                                                Корзина
                                            </Checkbox>
                                        </Col>
                                    </Row>
                                    <Row className="table-container">
                                        <Table<UserDataType>
                                            bordered
                                            columns={columns as ColumnTypes}
                                            components={components}
                                            dataSource={makeTableData()}
                                            locale={{
                                                emptyText: (
                                                    <Col
                                                        className="empty-text p3"
                                                        flex={1}
                                                    >
                                                        <Row>Таких приглашений нет :)</Row>
                                                    </Col>
                                                ),
                                            }}
                                            pagination={false}
                                            rowClassName={() => 'editable-row'}
                                            rowSelection={userRowSelection}
                                            scroll={{
                                                x: 'max-content',
                                                y: 77 * 7,
                                            }}
                                        />
                                    </Row>
                                    <Row className="pagination-row">
                                        {state.usersMeta?.total != null &&
                                            state.page != state.usersMeta?.last_page && (
                                                <Button
                                                    className="p3"
                                                    onClick={loadMore}
                                                    type="primary"
                                                >
                                                    Показать ещё
                                                </Button>
                                            )}
                                        <Pagination
                                            align="end"
                                            current={state.page}
                                            pageSize={state.perPage}
                                            pageSizeOptions={[
                                                10,
                                                20,
                                                50,
                                                100,
                                            ]}
                                            total={state.usersMeta?.total ?? 0}
                                            onChange={onPageOrPerPageChange}
                                            showSizeChanger
                                        />
                                    </Row>
                                </Col>
                            </Row>
                            <Row className="p2 controls-row">
                                <Col>
                                    <Row>
                                        {anyChanges && (
                                            <Tooltip
                                                placement="topLeft"
                                                title={
                                                    countUnfinishedUsers() > 0
                                                        ? `Не заполненных пользователей: ${countUnfinishedUsers()}`
                                                        : null
                                                }
                                            >
                                                <Button
                                                    onClick={() => {
                                                        if (
                                                            anyChanges &&
                                                            countUnfinishedUsers() == 0
                                                        ) {
                                                            saveChanges();
                                                        }
                                                    }}
                                                >
                                                    Сохранить
                                                </Button>
                                            </Tooltip>
                                        )}
                                        {anyChanges && (
                                            <Popconfirm
                                                cancelText="Отмена"
                                                disabled={!anyChanges}
                                                okText="ОК"
                                                onConfirm={() => {
                                                    state.listMode = 'list';
                                                }}
                                                title="Не сохранённые изменения будут потеряны"
                                            >
                                                <Button>Отмена</Button>
                                            </Popconfirm>
                                        )}
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                )}
            </div>
        </MainLayout>
    );
};

export default UserList;
