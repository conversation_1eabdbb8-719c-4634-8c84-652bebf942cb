@use '../../../styles/colors';

#uni-layout {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    min-height: 100vh;
    height: auto !important;

    .main-container {
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        margin-left: 248px;
        min-height: 100vh;
        overflow-x: auto;
        position: relative;
        width: 100%;

        &.sbm {
            margin-left: 96px;
        }
        .button-group {
            .ant-row {
                .default,
                .active {
                    border: 1px solid colors.$neutral50;
                    border-radius: 4px 4px 0 0;
                    color: colors.$neutral700;
                    min-height: 48px;
                    min-width: 104px;
                    transition: none;
                    white-space: nowrap;
                }
                .default {
                    background-color: colors.$neutral10;
                }
                .default:hover {
                    background-color: colors.$accentW50;
                }
                .default:focus {
                    background-color: colors.$accentW500;
                }
                .active,
                .active:hover,
                .active:focus {
                    background-color: colors.$neutral0;
                    border-bottom: none;
                }
            }
        }

        .child-container {
            align-items: flex-start;
            background: colors.$neutral50;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            height: calc(100% - 96px);
            margin-top: 96px;
            min-height: calc(100vh - 96px);
            padding: 48px;
        }
    }
    &.list-min-width .child-container {
        min-height: calc(100vh - 111px);
        min-width: 652px;
    }
    &.profile-min-width .child-container {
        min-height: calc(100vh - 111px);
        min-width: 652px;
    }
}
@media only screen and (min-width: 652px) {
    #uni-layout.list-min-width .child-container,
    #uni-layout.profile-min-width .child-container {
        min-height: calc(100vh - 111px);
    }
}
@media only screen and (orientation: portrait) {
    #uni-layout {
        .main-container {
            &:not(.sbm) {
                margin-left: 100%;
            }
            &.sbm {
                margin-left: 40px;
            }
            .child-container {
                height: calc(100% - 64px);
                margin-top: 64px;
                min-height: calc(100vh - 64px);
                padding: 8px;
            }
        }
        &.list-min-width .child-container {
            min-width: 350px;
        }
        &.profile-min-width .child-container {
            min-width: 350px;
        }
    }
}
