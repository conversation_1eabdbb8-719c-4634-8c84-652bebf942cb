import { TopBar } from '@components/ui/topBar/topBar';
import { ReactNode } from 'react';
import { rootStore } from '@store/instanse';
import { observer } from 'mobx-react';
import './layout.scss';

type UniLayoutProps = {
    activeTab: string;
    additionalClass?: string;
    paddingNone?: boolean;
    showSearch?: boolean;
    tabSet:
        | 'simulations'
        | 'constructor-new'
        | 'constructor'
        | 'management'
        | 'controls'
        | 'dev'
        | null;
    topBarText?: string;
    children: ReactNode;
};

const MainLayout: React.FC<UniLayoutProps> = observer(
    ({
        activeTab,
        children,
        paddingNone = false,
        showSearch = false,
        tabSet,
        topBarText = null,
    }): JSX.Element => {
        return (
            <>
                <TopBar
                    activeTab={activeTab}
                    tabSet={tabSet}
                    showSearch={showSearch}
                    topBarText={topBarText}
                />
                <div
                    className="child-container"
                    onClick={(e: React.MouseEvent<HTMLElement>) => {
                        if ((e.target as HTMLElement)?.className == 'child-container')
                            rootStore.ingameStore.sideBarMinimized = true;
                    }}
                    style={paddingNone ? { padding: '0px' } : {}}
                >
                    {children}
                </div>
            </>
        );
    },
);

export default MainLayout;
