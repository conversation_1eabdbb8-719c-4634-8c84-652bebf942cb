import { observer } from 'mobx-react';
import { Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import LeftSideBar from '../leftSideBar/leftSideBar';
import { rootStore } from '@store/instanse';
import { useMergedRouteHandle } from '@hooks/useMergedRouteHandle';
import './layout.scss';

export const LayoutWrapper = observer(() => {
    const handle = useMergedRouteHandle();

    return (
        <div
            id="uni-layout"
            className={`${handle.additionalClass || ''}`}
        >
            <LeftSideBar />
            <div
                className={`main-container${rootStore.ingameStore.sideBarMinimized ? ' sbm' : ''}`}
            >
                <Suspense>
                    <Outlet />
                </Suspense>
            </div>
        </div>
    );
});
