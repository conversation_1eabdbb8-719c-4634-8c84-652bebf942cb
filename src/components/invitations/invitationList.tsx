import { CRMAPIManager } from '@api/crmApiManager';
import { InvitationListResp } from '@api/responseModels/invitations/invitationListResponse';
import MainLayout from '@components/ui/layout/mainLayout';
import { useReactive } from 'ahooks';
import {
    Button,
    Checkbox,
    Col,
    ConfigProvider,
    Dropdown,
    Form,
    GetRef,
    Input,
    InputNumber,
    InputRef,
    List,
    message,
    Pagination,
    Popconfirm,
    Radio,
    Row,
    Select,
    Space,
    Table,
    TableProps,
    Tag,
    Tooltip,
} from 'antd';
import { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { TInvitation } from 'types/invitations/invitation';
import { Permissions } from '@classes/permissions';
import { InvitationResp } from '@api/responseModels/invitations/invitationResponse';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { GlobalConstants } from '@classes/constants';
import { ListModeSwitch } from '@components/ui/listModeSwitch/listModeSwitch';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFolderOpen } from '@fortawesome/free-solid-svg-icons';
import { Common } from '@classes/common';
import { rootStore } from '@store/instanse';
import { TableRowSelection } from 'antd/es/table/interface';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import React from 'react';
import _ from 'lodash';
import { Loader } from '@components/ui/loader/loader';
import dayjs from 'dayjs';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { TFilter } from 'types/filter';
import { TMetadata } from 'types/api/metadata';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { FilterResp } from '@api/responseModels/filters/filterResponse';
import { FilterDrawer } from '@components/drawers/filterDrawer';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';
import { LinkButton } from '@components/ui/linkButton/linkButton';
import { TRole } from 'types/user/role';
import { RoleListResp } from '@api/responseModels/roles/roleListResponse';
import Colors from '@classes/colors';

import './invitationList.scss';

type FormInstance<T> = GetRef<typeof Form<T>>;

const EditableContext = React.createContext<FormInstance<any> | null>(null);

type InviteDataType = TInvitation & {
    key: TInvitation['id'];
    deleted: boolean;
    idtStatus: { color: string; text: string }[];
    roles: TRole[];
};

interface EditableRowProps {
    index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
    const [form] = Form.useForm();
    return (
        <Form
            form={form}
            component={false}
        >
            <EditableContext.Provider value={form}>
                <tr {...props} />
            </EditableContext.Provider>
        </Form>
    );
};

interface EditableCellProps {
    title: React.ReactNode;
    editable: boolean;
    dataIndex: keyof InviteDataType;
    record: InviteDataType;
    handleSave: (record: InviteDataType) => void;
}

const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
    title,
    editable,
    children,
    dataIndex,
    record,
    handleSave,
    ...restProps
}) => {
    const [editing, setEditing] = useState(false);
    const inputRef = useRef<InputRef>(null);
    const selectRef = useRef(null);
    const form = useContext(EditableContext)!;

    useEffect(() => {
        if (editing) {
            inputRef?.current?.focus();
            selectRef?.current?.focus();
        }
    }, [editing]);

    const toggleEdit = () => {
        setEditing(!editing);
        form.setFieldsValue({ [dataIndex]: record[dataIndex] });
    };

    const save = async () => {
        try {
            const values = await form.validateFields();

            toggleEdit();
            handleSave({ ...record, ...values });
        } catch (errInfo) {
            console.log('Save failed:', errInfo);
        }
    };

    let childNode = children;

    if (editable && record.status != 'Принято' && !(record.deleted || record.deleted_at != null)) {
        childNode =
            editing && (dataIndex == 'role' || record.status != 'Отправлено') ? (
                <Form.Item
                    style={{ margin: 0 }}
                    name={dataIndex}
                    rules={
                        dataIndex == 'email'
                            ? [
                                  {
                                      type: 'email',
                                      message: 'Некорректное значение',
                                  },
                                  {
                                      required: true,
                                      message: `${title} обязательное поле.`,
                                  },
                              ]
                            : dataIndex == 'login'
                              ? [
                                    {
                                        pattern: /^(?=.*[A-Za-z0-9]$)[A-Za-z][A-Za-z\d]{2,15}$/,
                                        message: 'От 3 до 16 символов, только латиница и цифры',
                                    },
                                    {
                                        required: true,
                                        message: `${title} обязательное поле.`,
                                    },
                                ]
                              : [
                                    {
                                        required: true,
                                        message: `${title} обязательное поле.`,
                                    },
                                ]
                    }
                >
                    {dataIndex == 'role' && (
                        <Select
                            ref={selectRef}
                            onBlur={save}
                            onSelect={save}
                            options={record.roles
                                .filter((r) => r.name != 'Roles.Admin')
                                .map((r) => {
                                    return {
                                        value: r.name,
                                        label: (
                                            <span className="p3">
                                                {Common.makeRoleName(r.name)}
                                            </span>
                                        ),
                                    };
                                })}
                            placeholder="Выберите роль"
                        />
                    )}
                    {dataIndex == 'email' && (
                        <Input
                            ref={inputRef}
                            maxLength={50}
                            onPressEnter={save}
                            onBlur={save}
                            placeholder="Введите email"
                            required
                        />
                    )}
                    {dataIndex == 'login' && (
                        <Input
                            ref={inputRef}
                            maxLength={32}
                            onPressEnter={save}
                            onBlur={save}
                            placeholder="Введите логин"
                            required
                        />
                    )}
                </Form.Item>
            ) : (
                <div
                    className="editable-cell-value-wrap"
                    style={{ paddingInlineEnd: 4 }}
                    onClick={toggleEdit}
                >
                    {children}
                </div>
            );
    }

    return <td {...restProps}>{childNode}</td>;
};

type ColumnTypes = Exclude<TableProps<InviteDataType>['columns'], undefined>;

type TInviteChange = TInvitation & {
    change_type: 'add' | 'update' | 'remove' | 'restore';
};

type TState = {
    addAmount: number;
    addWithFilters: TFilter['id'][];
    addWithRole: TInvitation['role'];
    changes: { [key: TInvitation['id']]: TInviteChange };
    filterPickerInvitation: InviteDataType | null;
    filterPickerOpen: boolean;
    filterPickerTempFilters: TFilter['id'][];
    filters: TFilter[];
    filtersMeta: TMetadata | null;
    invitations: TInvitation[];
    invitationsMeta: TMetadata | null;
    isLoading: boolean;
    // Для создавемых id берётся из убывающего int,
    // чтобы избежать пересечений
    lastInvId: number;
    listMode: 'list' | 'table';
    mode: 'current' | 'deleted' | 'all';
    page: number;
    perPage: number;
    roles: TRole[];
    selectedInvRows: React.Key[];
    status: 'sent' | 'accepted' | 'all';
};

const InvitationList = (): JSX.Element => {
    const state = useReactive<TState>({
        addAmount: 1,
        addWithFilters: [],
        addWithRole: 'Roles.Client',
        changes: {},
        filterPickerInvitation: null,
        filterPickerTempFilters: [],
        filterPickerOpen: false,
        filters: [],
        filtersMeta: null,
        invitations: [],
        invitationsMeta: null,
        isLoading: false,
        lastInvId: -1,
        listMode: 'list',
        mode: 'current',
        page: 1,
        perPage: 10,
        roles: [],
        selectedInvRows: [],
        status: 'all',
    });
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const [searchParams] = useSearchParams();
    const editable =
        Permissions.checkPermission(Permissions.InvitationCreate) &&
        Permissions.checkPermission(Permissions.InvitationDelete) &&
        Permissions.checkPermission(Permissions.InvitationRestore) &&
        Permissions.checkPermission(Permissions.InvitationUpdate);
    const filtersEditable =
        Permissions.checkPermission(Permissions.FilterList) &&
        Permissions.checkPermission(Permissions.FilterGet) &&
        Permissions.checkPermission(Permissions.FilterCreate) &&
        Permissions.checkPermission(Permissions.FilterUpdate) &&
        Permissions.checkPermission(Permissions.FilterDelete) &&
        Permissions.checkPermission(Permissions.FilterRestore) &&
        Permissions.checkPermission(Permissions.FilterBulkAdd) &&
        Permissions.checkPermission(Permissions.FilterBulkResult);
    const anyChanges =
        Object.keys(state.changes).filter(
            (sckey) =>
                !(
                    state.changes[sckey].status == 'new' &&
                    state.changes[sckey].change_type == 'remove'
                ),
        ).length > 0;
    const components = {
        body: {
            row: EditableRow,
            cell: EditableCell,
        },
    };
    const tableStatuses = {
        accepted: GlobalConstants.TableStatuses['accepted'],
        deleted: GlobalConstants.TableStatuses['deleted'],
        new: GlobalConstants.TableStatuses['new'],
        restored: GlobalConstants.TableStatuses['restored'],
        sent: GlobalConstants.TableStatuses['sent'],
        unfilled: GlobalConstants.TableStatuses['unfilled'],
        updated: GlobalConstants.TableStatuses['updated'],
    };
    const defaultColumns: (ColumnTypes[number] & { editable?: boolean; dataIndex: string })[] = [
        {
            title: 'ID приглашения',
            dataIndex: 'id',
            render: (value) => (
                <span className="table-id p3">
                    {value}
                    {!value.includes('new-') && (
                        <Row>
                            <CopyButton
                                size={20}
                                textToCopy={`Приглашение ${value}`}
                                textToShow={'ID приглашения скопирован'}
                            />
                            <LinkButton
                                size={20}
                                textToCopy={Common.makeInviteLink(value)}
                                textToShow={'Ссылка на приглашение скопирована'}
                            />
                            <Tooltip
                                title={anyChanges ? 'Сохраните изменения для перехода' : 'Перейти'}
                            >
                                <Button
                                    className="open-btn"
                                    disabled={anyChanges}
                                    icon={<FontAwesomeIcon icon={faFolderOpen} />}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        if (!anyChanges) {
                                            navigate(`/management/invitations/${value}`);
                                        }
                                    }}
                                    type="text"
                                />
                            </Tooltip>
                        </Row>
                    )}
                </span>
            ),
            sorter: (a, b) => {
                if (a.id.includes('new-') && b.id.includes('new-')) {
                    return +a.id.replace('new-', '') - +b.id.replace('new-', '');
                } else if (a.id.includes('new-') && !b.id.includes('new-')) {
                    return 1;
                } else if (!a.id.includes('new-') && b.id.includes('new-')) {
                    return -1;
                } else {
                    return a.id.localeCompare(b.id);
                }
            },
            width: 200,
        },
        {
            title: 'Состояние',
            dataIndex: 'idtStatus',
            filters: Object.keys(tableStatuses).map((tsk) => {
                const tsv = tableStatuses[tsk];
                return {
                    text: (
                        <FilterButton
                            key={tsk}
                            hex={tsv.color}
                            text={tsv.text}
                        />
                    ),
                    value: tsv.text,
                };
            }),
            onFilter: (value, record) => {
                return record.idtStatus.find((si) => si.text.includes('' + value)) != undefined;
            },
            render: (_, rec) => (
                <Row className="table-status">
                    {rec.idtStatus.map((si, i) => (
                        <FilterButton
                            key={`status-${i}`}
                            hex={si.color}
                            text={si.text}
                        />
                    ))}
                    {rec.idtStatus.length == 0 && <span className="p3 gray">Нет статусов</span>}
                </Row>
            ),
            width: 200,
        },
        {
            title: 'Отправитель',
            dataIndex: 'sender_id',
            filters: _.uniq(
                state.invitations.map((i) => {
                    const isSystem = i.sender_id == null || i.sender_id == 'None';
                    return {
                        text: <span className="p3">{isSystem ? 'Система' : i.sender_name}</span>,
                        value: i.sender_id,
                    };
                }),
            ),
            onFilter: (value, record) => {
                return record.sender_id == value;
            },
            render: (_, rec) => {
                const getUserName = () =>
                    rec.sender_name == null || rec.sender_name.length == 0
                        ? `- ${rec.sender_id}`
                        : rec.sender_name;
                return rec.sender_id == null ? (
                    <Row className="table-user not-selected">
                        <Col className="p3 user-name">Не выбран</Col>
                    </Row>
                ) : (
                    <Popconfirm
                        cancelText="Отмена"
                        disabled={!anyChanges}
                        okText="ОК"
                        onConfirm={() => {
                            navigate(`/management/users/${rec.sender_id}`);
                        }}
                        title="Не сохранённые изменения будут потеряны"
                    >
                        <Row
                            className="table-user"
                            onClick={() => {
                                if (!anyChanges) {
                                    navigate(`/management/users/${rec.sender_id}`);
                                }
                            }}
                        >
                            <Col className="user-avatar">
                                {rec.sender_picture == null && (
                                    <div className="p1-strong">{getUserName()[0]}</div>
                                )}
                            </Col>
                            <Col className="p3 user-name">
                                <Button
                                    className="creator-btn"
                                    type="link"
                                >
                                    {getUserName()}
                                </Button>
                            </Col>
                        </Row>
                    </Popconfirm>
                );
            },
            sorter: (a, b) => {
                if (a.sender_id == null && b.sender_id == null) {
                    return 0;
                } else if (a.sender_id == null && b.sender_id != null) {
                    return 1;
                } else if (a.sender_id != null && b.sender_id == null) {
                    return -1;
                } else {
                    return a.sender_id.localeCompare(b.sender_id);
                }
            },
            width: 200,
        },
        {
            title: 'Роль',
            dataIndex: 'role',
            editable: true,
            render: (value) => <span className="p3">{value}</span>,
            width: 200,
        },
        {
            title: 'Фильтры',
            dataIndex: 'filters',
            filters: state.filters.map((fi) => {
                return {
                    text: (
                        <FilterButton
                            key={fi.id}
                            hex={fi.color_hex}
                            text={fi.name}
                        />
                    ),
                    value: fi.name,
                };
            }),
            onFilter: (value, record) => {
                return (
                    record.filters.find((filter) => filter.name.includes('' + value)) != undefined
                );
            },
            render: (_, record) => (
                <Row
                    className="filter-row"
                    onClick={() => {
                        if (
                            editable &&
                            filtersEditable &&
                            record.deleted_at == null &&
                            (record.id.includes('new-') || record.status == 'Отправлено')
                        ) {
                            state.filterPickerInvitation = { ...record };
                            setFilterPickerOpen(true);
                        }
                    }}
                >
                    {record.filters.slice(0, 3).map((fi) => (
                        <FilterButton
                            key={`${record.id}-${fi.id}`}
                            hex={fi.color_hex}
                            text={fi.name}
                        />
                    ))}
                    {record.filters.length > 3 && (
                        <Dropdown
                            menu={{
                                items: record.filters.slice(3, record.filters.length).map((f) => {
                                    return {
                                        key: f.id,
                                        label: (
                                            <FilterButton
                                                key={`filter-${record.id}-${f.id}`}
                                                hex={f.color_hex}
                                                text={f.name}
                                            />
                                        ),
                                    };
                                }),
                            }}
                            trigger={['hover', 'click']}
                        >
                            <Row>
                                <FilterButton
                                    key="more-filters"
                                    hex={Colors.Neutral[50]}
                                    text={`+${record.filters.length - 3}`}
                                />
                            </Row>
                        </Dropdown>
                    )}
                    {record.filters.length == 0 && <span className="p3">Нет фильтров</span>}
                </Row>
            ),
            width: 250,
        },
        {
            title: 'Логин',
            dataIndex: 'login',
            editable: true,
            render: (value, record) =>
                value == '' ? (
                    <span className="not-selected">Введите логин</span>
                ) : (
                    <Row>
                        <span className="p3">{value}</span>
                        {!record.id.includes('new-') && (
                            <CopyButton
                                size={20}
                                textToCopy={value}
                                textToShow={'Логин скопирован'}
                            />
                        )}
                    </Row>
                ),
            width: 200,
        },
        {
            title: 'Email',
            dataIndex: 'email',
            editable: true,
            render: (value, record) =>
                value == '' ? (
                    <span className="not-selected">Введите email</span>
                ) : (
                    <Row>
                        <span className="p3">{value}</span>
                        {!record.id.includes('new-') && (
                            <CopyButton
                                size={20}
                                textToCopy={value}
                                textToShow={'Email скопирован'}
                            />
                        )}
                    </Row>
                ),
            width: 250,
        },
        {
            title: 'Пользователь',
            dataIndex: 'user_id',
            render: (_, rec) => {
                const getUserName = () =>
                    rec.user_name == null || rec.user_name.length == 0
                        ? `- ${rec.user_id}`
                        : rec.user_name;
                return rec.user_id == null ? (
                    <Row className="table-user not-selected">
                        <Col className="p3 user-name">Не создан</Col>
                    </Row>
                ) : (
                    <Popconfirm
                        cancelText="Отмена"
                        disabled={!anyChanges}
                        okText="ОК"
                        onConfirm={() => {
                            navigate(`/management/users/${rec.user_id}`);
                        }}
                        title="Не сохранённые изменения будут потеряны"
                    >
                        <Row
                            className="table-user"
                            onClick={() => {
                                if (!anyChanges) {
                                    navigate(`/management/users/${rec.user_id}`);
                                }
                            }}
                        >
                            <Col className="user-avatar">
                                {rec.user_picture == null && (
                                    <div className="p1-strong">{getUserName()[0]}</div>
                                )}
                            </Col>
                            <Col className="p3 user-name">
                                <Button
                                    className="creator-btn"
                                    type="link"
                                >
                                    {getUserName()}
                                </Button>
                            </Col>
                        </Row>
                    </Popconfirm>
                );
            },
            sorter: (a, b) => {
                if (a.user_id == null && b.user_id == null) {
                    return 0;
                } else if (a.user_id == null && b.user_id != null) {
                    return 1;
                } else if (a.user_id != null && b.user_id == null) {
                    return -1;
                } else {
                    return a.user_id.localeCompare(b.user_id);
                }
            },
            width: 200,
        },
        {
            title: 'Создано',
            dataIndex: 'created_at',
            render: (value) => (
                <span className="p3 lighter-tone">{Common.formatDateString(value)}</span>
            ),
            sorter: (a, b) => {
                const aTime = new Date(a.created_at).getTime();
                const bTime = new Date(b.created_at).getTime();
                return aTime - bTime;
            },
            width: 200,
        },
        {
            title: 'Изменено',
            dataIndex: 'updated_at',
            render: (value) => (
                <span className="p3 lighter-tone">{Common.formatDateString(value)}</span>
            ),
            sorter: (a, b) => {
                const aTime = new Date(a.updated_at).getTime();
                const bTime = new Date(b.updated_at).getTime();
                return aTime - bTime;
            },
            width: 200,
        },
    ];

    function updateTableInvite(record: InviteDataType) {
        if (Object.keys(state.changes).includes(record.id)) {
            const originalInv = state.invitations.find((inv) => inv.id == record.id);
            if (
                originalInv != undefined &&
                state.changes[record.id].change_type != 'remove' &&
                _.isEqual(record, {
                    ...originalInv,
                    change_type: 'update',
                    key: record.key,
                    deleted: record.deleted,
                    idtStatus: record.idtStatus,
                })
            ) {
                delete state.changes[record.id];
            } else {
                state.changes[record.id] = {
                    ...state.changes[record.id],
                    ...record,
                };
            }
        } else {
            const originalInv = state.invitations.find((inv) => inv.id == record.id);
            if (
                !_.isEqual(record, {
                    ...originalInv,
                    key: originalInv.id,
                    deleted: false,
                    idtStatus: [],
                })
            ) {
                state.changes[record.id] = {
                    ...record,
                    change_type: 'update',
                };
            }
        }
    }

    const columns = defaultColumns.map((col) => {
        if (!col.editable) {
            return col;
        }
        return {
            ...col,
            onCell: (record: InviteDataType) => ({
                record,
                editable: col.editable,
                dataIndex: col.dataIndex,
                title: col.title,
                handleSave: updateTableInvite,
            }),
        };
    });

    async function loadInvitations(page: number = state.page, addition: boolean = false) {
        if (!Permissions.checkPermission(Permissions.InvitationList)) {
            navigate('/lk');
            message.error('Недостаточно прав для просмотра списка приглашений');
            return;
        }
        state.isLoading = true;
        try {
            const sender_id = searchParams.get('sender_id');
            const result = await CRMAPIManager.request<InvitationListResp>(async (api) => {
                return await api.getInvitationList({
                    status:
                        state.status == 'all'
                            ? null
                            : state.status == 'accepted'
                              ? 'Принято'
                              : 'Отправлено',
                    page: page,
                    per_page: state.perPage,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        sender: state.listMode == 'list' ? sender_id : null,
                        created_at: null,
                        deleted:
                            state.mode == 'current'
                                ? 'null'
                                : state.mode == 'deleted'
                                  ? 'only'
                                  : 'all',
                    },
                });
            });
            if (result.errorMessages) throw result.errorMessages;
            state.invitations = addition
                ? [...state.invitations, ...result.data.data]
                : result.data.data;
            state.invitationsMeta = result.data.meta;
        } catch (err) {
            messageApi.error('Ошибка при получении списка приглашений');
            console.log(err);
        }
        state.isLoading = false;
    }

    async function loadFilters() {
        state.isLoading = true;
        try {
            const filters = await CRMAPIManager.getAll<FilterListResp>(
                async (api, page, per_page) => {
                    return await api.getFilterList({
                        query: null,
                        page: page,
                        per_page: per_page,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            deleted: 'null',
                        },
                    });
                },
            );
            if (filters.errorMessages) throw filters.errorMessages;
            state.filters = filters.data.data;
            state.filtersMeta = filters.data.meta;
        } catch (err) {
            messageApi.error('Ошибка при получении списка фильтров');
            console.log(err);
        }
        state.isLoading = false;
    }

    async function loadRoles() {
        state.isLoading = true;
        try {
            const roles = await CRMAPIManager.request<RoleListResp>(async (api) => {
                return await api.getRoleList();
            });
            if (roles.errorMessages) throw roles.errorMessages;
            state.roles = roles.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при получении списка ролей');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function makeListItems() {
        const arr = [];
        if (Permissions.checkPermission(Permissions.InvitationCreate) && state.mode == 'current') {
            arr.push('add');
        }
        arr.push(...state.invitations);
        return arr;
    }

    async function loadNecessary() {
        if (Permissions.checkPermission(Permissions.RoleList)) {
            await loadRoles();
        }
        state.changes = {};
        state.lastInvId = -1;
        state.mode = 'current';
        state.selectedInvRows = [];
        await loadFilters();
        await loadInvitations();
    }

    useEffect(() => {
        loadNecessary();
    }, []);

    useEffect(() => {
        state.page = 1;
        loadInvitations();
    }, [state.mode, state.status]);

    async function listModeChange(value: TState['listMode']) {
        if (value == 'table') {
            state.mode = 'current';
            state.status = 'all';
        }
        state.listMode = value;
    }

    async function restoreInvitation(inv: TInvitation) {
        messageApi.open({
            type: 'loading',
            content: 'Восстанавливаем...',
            duration: 0,
        });
        try {
            const result = await CRMAPIManager.request<InvitationResp>(async (api) => {
                return await api.restoreInvitation(inv.id);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadInvitations();
            messageApi.destroy();
            messageApi.success('Приглашение восстановлено');
        } catch (errors) {
            messageApi.destroy();
            messageApi.error('Ошибка :(');
            console.log(errors);
        }
    }

    function makeInvitation(): TInvitation {
        const curUser = rootStore.currentUserStore.getUser;
        const newInvite: TInvitation = {
            id: `new${state.lastInvId}`,
            email: `email${state.invitationsMeta.total - state.lastInvId}@example.com`,
            filters: state.addWithFilters.map((awf) => state.filters.find((f) => f.id == awf)),
            login: `user${state.invitationsMeta.total - state.lastInvId}`,
            role: state.addWithRole,
            sender_id: curUser.id,
            sender_name: curUser.name,
            sender_picture: curUser.picture,
            status: 'Новое',
            user_id: null,
            user_name: null,
            user_picture: null,
            created_at: Common.dateNowString(),
            updated_at: null,
            deleted_at: null,
        };
        state.lastInvId -= 1;
        return newInvite;
    }

    function getStatusList(invite: TInvitation) {
        const statuses: InviteDataType['idtStatus'] = [];
        if (invite.id.startsWith('new-')) {
            statuses.push(tableStatuses['new']);
        }
        if (
            invite.deleted_at != null ||
            (state.changes[invite.id] != undefined &&
                state.changes[invite.id].change_type == 'remove')
        ) {
            statuses.push(tableStatuses['deleted']);
        }
        if (
            state.changes[invite.id] != undefined &&
            state.changes[invite.id].change_type == 'restore'
        ) {
            statuses.push(tableStatuses['restored']);
        }
        if (
            state.changes[invite.id] != undefined &&
            state.changes[invite.id].change_type == 'update'
        ) {
            statuses.push(tableStatuses['updated']);
        }
        switch (invite.status) {
            case 'Отправлено': {
                statuses.push(tableStatuses['sent']);
                break;
            }
            case 'Принято': {
                statuses.push(tableStatuses['accepted']);
                break;
            }
        }
        if (invite.email.trim().length == 0 || invite.login.trim().length == 0) {
            statuses.push(tableStatuses['unfilled']);
        }
        return statuses;
    }

    function makeTableData(): InviteDataType[] {
        const tableData = state.invitations.map((inv) => {
            const change = state.changes[inv.id];
            const tempInv = change != undefined ? change : inv;
            const deleted =
                change == undefined ? inv.deleted_at != null : change.change_type == 'remove';
            return {
                ...tempInv,
                key: tempInv.id,
                deleted: deleted,
                idtStatus: getStatusList(tempInv),
                roles: state.roles,
            };
        });
        return tableData.filter((tdi) => (state.mode == 'current' ? !tdi.deleted : tdi.deleted));
    }

    const invRowSelection: TableRowSelection<InviteDataType> = {
        columnWidth: 40,
        getCheckboxProps: (inv: InviteDataType) => ({
            disabled:
                !editable ||
                !(inv.status == 'Отправлено' || inv.status == 'Новое' || inv.status == 'Удалено'),
            name: inv.id,
        }),
        onChange: (srk) => (state.selectedInvRows = srk),
        selectedRowKeys: state.selectedInvRows,
    };

    function addNewInvitations() {
        const amount = state.addAmount;
        const newInvList = [];
        const tempChanges = { ...state.changes };
        const rowKeySelectList = [];
        for (let i = 0; i < amount; i++) {
            const newInv = makeInvitation();
            newInvList.push(newInv);
            tempChanges[newInv.id] = {
                ...newInv,
                change_type: 'add',
            };
            rowKeySelectList.push(newInv.id);
        }
        state.changes = tempChanges;
        state.invitations = [
            ...state.invitations,
            ...newInvList,
        ];
        state.selectedInvRows = rowKeySelectList;
    }

    function makeChangeStats() {
        const values = Object.values(state.changes);
        let chDelete = 0;
        let chEdit = 0;
        let chNew = 0;
        let chRestore = 0;
        for (let i = 0; i < values.length; i++) {
            if (values[i].id.includes('new-')) chNew += 1;
            switch (values[i].change_type) {
                case 'remove': {
                    chDelete += 1;
                    break;
                }
                case 'update': {
                    chEdit += 1;
                    break;
                }
                case 'restore': {
                    chRestore += 1;
                    break;
                }
            }
        }
        return `(новые: ${chNew}, изменены: ${chEdit}, удалены: ${chDelete}, восстановлены: ${chRestore})`;
    }

    function deleteSelected() {
        const tempChanges = { ...state.changes };
        const tempSKR = [...state.selectedInvRows];
        for (let i = 0; i < tempSKR.length; i++) {
            const change = Object.keys(tempChanges).find((k) => k == tempSKR[i]);
            if (change != undefined) {
                if (tempChanges[change].change_type == 'restore') {
                    delete tempChanges[change];
                } else {
                    tempChanges[change].change_type = 'remove';
                }
            } else {
                tempChanges['' + tempSKR[i]] = {
                    ...state.invitations.find((inv) => inv.id == tempSKR[i]),
                    change_type: 'remove',
                };
            }
        }
        state.changes = tempChanges;
        state.selectedInvRows = [];
    }

    function restoreSelected() {
        const tempChanges = { ...state.changes };
        const tempSKR = [...state.selectedInvRows];
        for (let i = 0; i < tempSKR.length; i++) {
            const change = Object.keys(tempChanges).find((k) => k == tempSKR[i]);
            if (change != undefined) {
                if (tempChanges[change].id.startsWith('new-')) {
                    tempChanges[change].change_type = 'add';
                } else {
                    delete tempChanges[change];
                }
            } else {
                tempChanges['' + tempSKR[i]] = {
                    ...state.invitations.find((inv) => inv.id == tempSKR[i]),
                    change_type: 'restore',
                };
            }
        }
        state.changes = tempChanges;
        state.selectedInvRows = [];
    }

    const countUnfinishedInvites = useCallback(() => {
        const keys = Object.keys(state.changes);
        let count = 0;
        for (let i = 0; i < keys.length; i++) {
            const value = state.changes[keys[i]];
            if (
                value.change_type != 'remove' &&
                (value.login.trim().length == 0 || value.email.trim().length == 0)
            ) {
                count++;
            }
        }
        return count;
    }, [state.changes]);

    function prepareChanges(): TBulkUpdate<TInvitation> {
        return Object.keys(state.changes)
            .filter((key) => {
                const change = state.changes[key];
                return !(change.id.includes('new-') && change.change_type == 'remove');
            })
            .map((changeKey, i) => {
                const change = state.changes[changeKey];
                return {
                    action: change.change_type,
                    index: i + 1,
                    value: {
                        id: change.id,
                        email: change.email,
                        filters: change.filters.map((f) => {
                            return { ...f, target: 'users' };
                        }),
                        login: change.login,
                        role: change.role,
                        sender_id: change.sender_id,
                        sender_name: change.sender_name,
                        sender_picture: change.sender_picture,
                        status:
                            change.status == 'Отправлено' || change.status == 'Принято'
                                ? change.status
                                : change.change_type == 'add'
                                  ? 'Отправлено'
                                  : 'Удалено',
                        user_id: change.user_id,
                        user_name: change.user_name,
                        user_picture: change.user_picture,
                        created_at: change.created_at,
                        updated_at: change.updated_at,
                        deleted_at: change.deleted_at,
                    },
                };
            });
    }

    async function saveChanges() {
        const counter = countUnfinishedInvites();
        if (counter > 0) {
            messageApi.warning(`Остались не заполненные приглашения: ${counter} шт.`);
            return false;
        }
        state.isLoading = true;
        let returnValue = false;
        const bulkBody = prepareChanges();
        try {
            const result = await CRMAPIManager.bulkRequest<TInvitation>(
                bulkBody,
                async (api, _bulkBody) => {
                    return await api.bulkInvitation(_bulkBody);
                },
                async (api, task_id) => {
                    return await api.bulkResultInvitation(task_id);
                },
            );
            if (result.errorMessages) throw result.errorMessages;
            message.success('Изменения сохранены');
            await loadNecessary();
            returnValue = true;
        } catch (errors) {
            messageApi.error('Ошибка при сохранении, попробуйте ещё раз');
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    function prepareCSVData() {
        const csvData: string[][] = [
            [
                '№',
                'Логин',
                'Email',
                'Роль',
                'Ссылка',
            ],
        ];
        const srk = [...state.selectedInvRows];
        for (let i = 0; i < srk.length; i++) {
            const inv = state.invitations.find((invi) => invi.id == srk[i]);
            if (inv != undefined) {
                const invRow = [
                    `${i + 1}`,
                    inv.login,
                    Common.isNullOrEmptyString(inv.email) ? '-' : inv.email,
                    inv.role,
                    Common.makeInviteLink(inv.id),
                ];
                csvData.push(invRow);
            }
        }
        return csvData;
    }

    function exportInvitations() {
        const rows = prepareCSVData();
        const csvContent = '\ufeff' + rows.map((e) => e.join(';')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
        const a = document.createElement('a');
        a.style.display = 'none';
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        a.download = `Приглашения, ${dayjs.utc().tz(timezone).format('HH-mm-ss DD.MM.YYYY')}.csv`;
        a.href = URL.createObjectURL(blob);
        a.addEventListener('click', () => {
            setTimeout(() => URL.revokeObjectURL(a.href), 30 * 1000);
        });
        a.click();
        a.remove();
        URL.revokeObjectURL(a.href);
    }

    function updateSelInvitationFilters() {
        updateTableInvite({
            ...state.filterPickerInvitation,
            filters:
                state.filterPickerTempFilters.length > 0
                    ? state.filterPickerTempFilters.map((tfi) =>
                          state.filters.find((fi) => fi.id == tfi),
                      )
                    : [],
        });
        state.filterPickerOpen = false;
        state.filterPickerTempFilters = [];
        state.filterPickerInvitation = null;
    }

    async function onFilterAdd(filter: TFilter) {
        if (!filtersEditable) {
            messageApi.error('Запрещена работа с фильтрами');
            return;
        }
        state.isLoading = true;
        try {
            const result = await CRMAPIManager.request<FilterResp>(async (api) => {
                return await api.createFilter(filter);
            });
            if (result.errorMessages) throw result.errorMessages;
            await loadFilters();
        } catch (errors) {
            messageApi.error('Ошибка при создании фильтра');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function updateSelInvitationTempFilters(filter: TFilter) {
        if (state.filterPickerTempFilters.includes(filter.id)) {
            state.filterPickerTempFilters = state.filterPickerTempFilters.filter(
                (tfi) => tfi != filter.id,
            );
        } else {
            state.filterPickerTempFilters = [
                ...state.filterPickerTempFilters,
                filter.id,
            ];
        }
    }

    function setFilterPickerOpen(isOpen: boolean) {
        if (isOpen) {
            state.filterPickerTempFilters = state.filterPickerInvitation.filters.map((fi) => fi.id);
            state.filterPickerOpen = true;
        } else {
            state.filterPickerOpen = false;
            state.filterPickerTempFilters = [];
            state.filterPickerInvitation = null;
        }
    }

    async function resendEmails() {
        const selRows = state.selectedInvRows.map((sir) => sir.toString());
        state.isLoading = true;
        try {
            const inv = await CRMAPIManager.request<InvitationListResp>(async (api) => {
                return await api.resendInvitationEmails(selRows);
            });
            if (inv.errorMessages) throw inv.errorMessages;
            await loadInvitations();
            messageApi.success(`${selRows.length} писем отправлено`);
        } catch (error) {
            messageApi.error('Ошибка при повторной отправке писем');
            console.log(error);
        }
        state.isLoading = false;
    }

    async function onPageOrPerPageChange(page: number, pageSize: number) {
        if (page != state.page) {
            await loadInvitations(page);
            state.page = page;
        }
        if (pageSize != state.perPage) {
            if (state.invitationsMeta?.total == null || state.invitationsMeta?.total == 0) return;
            state.perPage = pageSize;
            state.page = Math.ceil(state.invitationsMeta?.from_ / pageSize);
            await loadInvitations();
        }
    }

    async function loadMore() {
        state.page += 1;
        await loadInvitations(state.page, true);
    }

    return (
        <MainLayout
            activeTab="invitations"
            additionalClass="list-min-width"
            tabSet="management"
        >
            <div className="invitation-list-container">
                {contextHolder}
                <PreventLeaving
                    anyChanges={anyChanges}
                    onSave={saveChanges}
                />
                {state.filtersMeta != null && (
                    <FilterDrawer
                        filterList={state.filters.filter((f) => !f.is_protected)}
                        filterListMeta={state.filtersMeta}
                        fixedTarget="users"
                        isOpen={state.filterPickerOpen}
                        onConfirm={updateSelInvitationFilters}
                        onFilterAdd={onFilterAdd}
                        onSelect={updateSelInvitationTempFilters}
                        selected={state.filterPickerTempFilters}
                        setIsOpen={setFilterPickerOpen}
                    />
                )}
                <div className="radio-group">
                    <Popconfirm
                        cancelText="Отмена"
                        disabled={state.listMode == 'list'}
                        okText="ОК"
                        onConfirm={() => {
                            listModeChange('list');
                        }}
                        title="Не сохранённые изменения будут потеряны"
                    >
                        <ListModeSwitch
                            onChange={listModeChange}
                            value={state.listMode}
                        />
                    </Popconfirm>
                    {state.listMode == 'list' && (
                        <Radio.Group
                            onChange={(e) => {
                                e.stopPropagation();
                                state.mode = e.target.value;
                            }}
                            options={[
                                { label: 'Текущие', value: 'current' },
                                { label: 'Корзина', value: 'deleted' },
                                { label: 'Все', value: 'all' },
                            ]}
                            optionType="button"
                            value={state.mode}
                        />
                    )}
                    {state.listMode == 'list' && (
                        <Radio.Group
                            onChange={(e) => {
                                e.stopPropagation();
                                state.status = e.target.value;
                            }}
                            options={[
                                { label: 'Отправлено', value: 'sent' },
                                { label: 'Принято', value: 'accepted' },
                                { label: 'Все', value: 'all' },
                            ]}
                            optionType="button"
                            value={state.status}
                        />
                    )}
                </div>
                {state.listMode == 'list' && (
                    <div className="invitation-list">
                        {searchParams.get('sender_id') && (
                            <div className="p3 tag-section">
                                <Tag
                                    closeIcon
                                    onClose={() => {
                                        searchParams.delete('sender_id');
                                        navigate('/management/invitations');
                                        loadInvitations();
                                    }}
                                >
                                    ID отправителя {searchParams.get('sender_id')}
                                </Tag>
                            </div>
                        )}
                        <ConfigProvider
                            theme={{
                                token: GlobalConstants.ListGridSettings,
                            }}
                        >
                            <List
                                dataSource={makeListItems()}
                                grid={GlobalConstants.ListGridCols}
                                loading={state.isLoading}
                                locale={{
                                    emptyText: (
                                        <Col
                                            className="empty-text p3"
                                            flex={1}
                                        >
                                            <Row>Таких приглашений нет :)</Row>
                                        </Col>
                                    ),
                                }}
                                renderItem={(item: 'add' | TInvitation) => {
                                    if (item == 'add')
                                        return (
                                            <div
                                                className="invitation-list-add"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    navigate('/management/invitations/new');
                                                }}
                                            >
                                                <div className="add-icon" />
                                            </div>
                                        );
                                    else
                                        return (
                                            <div
                                                className={`invitation-list-card${item.deleted_at != null ? ' del-options' : ''}`}
                                            >
                                                <Row className="header-row">
                                                    <div className="p2-strong">ID {item.id}</div>
                                                    <CopyButton
                                                        textToCopy={`Приглашение ${item.id}`}
                                                        textToShow="ID приглашения скопирован"
                                                        size={24}
                                                    />
                                                </Row>
                                                <Row className="body-row">
                                                    <Col flex={1}>
                                                        <Row
                                                            className="p2"
                                                            style={{
                                                                justifyContent: 'space-between',
                                                                width: '100%',
                                                            }}
                                                        >
                                                            <Col>
                                                                <Row>
                                                                    <span>От:&nbsp;</span>
                                                                    <Button
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            navigate(
                                                                                `/management/users/${item.sender_id}`,
                                                                            );
                                                                        }}
                                                                        type="link"
                                                                    >
                                                                        {item.sender_name}
                                                                    </Button>
                                                                </Row>
                                                            </Col>
                                                            <Col>Статус: {item.status}</Col>
                                                        </Row>
                                                        <Row
                                                            className="p2"
                                                            style={{
                                                                justifyContent: 'space-between',
                                                                width: '100%',
                                                            }}
                                                        >
                                                            <Col>
                                                                <Row>
                                                                    <Col>Email: {item.email}</Col>
                                                                    <Col>
                                                                        <CopyButton
                                                                            textToCopy={item.email}
                                                                            textToShow="Email скопирован"
                                                                            size={24}
                                                                        />
                                                                    </Col>
                                                                </Row>
                                                            </Col>
                                                            <Col>{item.role}</Col>
                                                        </Row>
                                                        <Row
                                                            className="p2"
                                                            style={{
                                                                justifyContent:
                                                                    item.user_id != null
                                                                        ? 'space-between'
                                                                        : 'flex-start',
                                                                width: '100%',
                                                            }}
                                                        >
                                                            <Col>
                                                                <Row>
                                                                    Логин: {item.login}
                                                                    <CopyButton
                                                                        textToCopy={item.login}
                                                                        textToShow="Логин скопирован"
                                                                        size={24}
                                                                    />
                                                                </Row>
                                                            </Col>
                                                            {item.user_id != null && (
                                                                <Col>
                                                                    <Button
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            navigate(
                                                                                `/management/users/${item.user_id}`,
                                                                            );
                                                                        }}
                                                                        type="link"
                                                                    >
                                                                        {item.user_name}
                                                                    </Button>
                                                                </Col>
                                                            )}
                                                        </Row>
                                                        <Row className="filters">
                                                            {item.filters.map((filter, i) => {
                                                                return (
                                                                    <FilterButton
                                                                        key={`user-${item.id}-${i}`}
                                                                        hex={filter.color_hex}
                                                                        text={filter.name}
                                                                    />
                                                                );
                                                            })}
                                                            {item.filters.length == 0 && (
                                                                <span className="p2">
                                                                    Без фильтров
                                                                </span>
                                                            )}
                                                        </Row>
                                                    </Col>
                                                </Row>
                                                <Row className="event-card-controls split-row p3">
                                                    <Col>
                                                        <Row>
                                                            <Button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    navigate(
                                                                        `/management/invitations/${item.id}`,
                                                                    );
                                                                }}
                                                            >
                                                                Открыть
                                                            </Button>
                                                            {item.deleted_at != null && (
                                                                <Button
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        restoreInvitation(item);
                                                                    }}
                                                                >
                                                                    Восстановить
                                                                </Button>
                                                            )}
                                                        </Row>
                                                    </Col>
                                                    {item.deleted_at != null && (
                                                        <Col>
                                                            <Button className="deleted-tag">
                                                                Удалено
                                                            </Button>
                                                        </Col>
                                                    )}
                                                </Row>
                                            </div>
                                        );
                                }}
                            />
                        </ConfigProvider>
                        <Row className="pagination-row">
                            {state.invitationsMeta?.total != null &&
                                state.page != state.invitationsMeta?.last_page && (
                                    <Button
                                        className="p3"
                                        onClick={loadMore}
                                        type="primary"
                                    >
                                        Показать ещё
                                    </Button>
                                )}
                            <Pagination
                                align="end"
                                current={state.page}
                                pageSize={state.perPage}
                                pageSizeOptions={[
                                    10,
                                    20,
                                    50,
                                    100,
                                ]}
                                total={state.invitationsMeta?.total ?? 0}
                                onChange={onPageOrPerPageChange}
                                showSizeChanger
                            />
                        </Row>
                    </div>
                )}
                {state.listMode == 'table' && (
                    <Row className="invitation-table">
                        {state.isLoading && <Loader />}
                        <Col>
                            <Row className="header-row">
                                <Col>
                                    <h4>
                                        {`Приглашений: ${
                                            state.invitations.length
                                        } ${makeChangeStats()}`}
                                    </h4>
                                </Col>
                            </Row>
                            <Row className="body-row">
                                <Col>
                                    <Row className="table-actions">
                                        <Col>
                                            <span className="selected p3">
                                                {state.selectedInvRows.length} выбрано
                                            </span>
                                        </Col>
                                        <Col>
                                            <Space.Compact
                                                block
                                                className="p3 add-group"
                                            >
                                                <Button
                                                    disabled={!editable || state.mode == 'deleted'}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        state.addAmount = 1;
                                                    }}
                                                    type={
                                                        state.addAmount == 1 ? 'primary' : 'default'
                                                    }
                                                >
                                                    1
                                                </Button>
                                                <Button
                                                    disabled={!editable || state.mode == 'deleted'}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        state.addAmount = 5;
                                                    }}
                                                    type={
                                                        state.addAmount == 5 ? 'primary' : 'default'
                                                    }
                                                >
                                                    5
                                                </Button>
                                                <Button
                                                    disabled={!editable || state.mode == 'deleted'}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        state.addAmount = 10;
                                                    }}
                                                    type={
                                                        state.addAmount == 10
                                                            ? 'primary'
                                                            : 'default'
                                                    }
                                                >
                                                    10
                                                </Button>
                                                <InputNumber
                                                    disabled={!editable || state.mode == 'deleted'}
                                                    max={50}
                                                    min={1}
                                                    onChange={(value) => {
                                                        state.addAmount = value;
                                                    }}
                                                    value={state.addAmount}
                                                />
                                                <Button
                                                    disabled={!editable || state.mode == 'deleted'}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        addNewInvitations();
                                                    }}
                                                    type="primary"
                                                >
                                                    +
                                                </Button>
                                            </Space.Compact>
                                        </Col>
                                        <Col>
                                            <Select
                                                allowClear
                                                className="creation-filter-select"
                                                disabled={!editable || state.mode == 'deleted'}
                                                filterOption={(
                                                    input: string,
                                                    option: {
                                                        label: React.ReactNode;
                                                        value: number;
                                                    },
                                                ) => {
                                                    const filter = state.filters.find(
                                                        (fi) => fi.id == option.value,
                                                    );
                                                    return filter.name
                                                        .toLocaleLowerCase()
                                                        .includes(input.toLocaleLowerCase());
                                                }}
                                                filterSort={(a, b) => {
                                                    const filterA = state.filters.find(
                                                        (fi) => fi.id == a.value,
                                                    );
                                                    const filterB = state.filters.find(
                                                        (fi) => fi.id == b.value,
                                                    );
                                                    return filterA?.name.localeCompare(
                                                        filterB?.name,
                                                    );
                                                }}
                                                maxTagCount={2}
                                                mode="multiple"
                                                notFoundContent={
                                                    <Col
                                                        className="empty-text p3"
                                                        flex={1}
                                                    >
                                                        <Row style={{ justifyContent: 'center' }}>
                                                            Таких фильтров нет :)
                                                        </Row>
                                                    </Col>
                                                }
                                                onClear={() => (state.addWithFilters = [])}
                                                onDeselect={(value) => {
                                                    state.addWithFilters =
                                                        state.addWithFilters.filter(
                                                            (sfi) => sfi != value,
                                                        );
                                                }}
                                                onSelect={(value) => {
                                                    state.addWithFilters = [
                                                        ...state.addWithFilters,
                                                        value,
                                                    ];
                                                }}
                                                options={state.filters
                                                    .filter(
                                                        (fi) =>
                                                            fi.target == 'users' &&
                                                            !fi.is_protected,
                                                    )
                                                    .map((fi) => {
                                                        return {
                                                            label: (
                                                                <FilterButton
                                                                    hex={fi.color_hex}
                                                                    text={fi.name}
                                                                />
                                                            ),
                                                            value: fi.id,
                                                        };
                                                    })}
                                                placeholder="Выберите фильтр"
                                                value={state.addWithFilters}
                                            />
                                        </Col>
                                        {state.roles.length != 0 && (
                                            <Col>
                                                <Select
                                                    allowClear={false}
                                                    className="creation-role-select"
                                                    disabled={!editable || state.mode == 'deleted'}
                                                    onSelect={(value) => {
                                                        state.addWithRole = value;
                                                    }}
                                                    options={state.roles
                                                        .filter((r) => r.name != 'Roles.Admin')
                                                        .map((r) => {
                                                            return {
                                                                label: Common.makeRoleName(r.name),
                                                                value: r.name,
                                                            };
                                                        })}
                                                    value={state.addWithRole}
                                                />
                                            </Col>
                                        )}
                                        {state.selectedInvRows.length > 0 &&
                                            state.mode == 'current' &&
                                            !anyChanges && (
                                                <Col>
                                                    <Button
                                                        disabled={!editable}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            resendEmails();
                                                        }}
                                                    >
                                                        Повторить письма
                                                    </Button>
                                                </Col>
                                            )}
                                        {state.selectedInvRows.length > 0 &&
                                            state.mode == 'current' && (
                                                <Col>
                                                    <Button
                                                        danger
                                                        disabled={!editable}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            deleteSelected();
                                                        }}
                                                    >
                                                        Удалить
                                                    </Button>
                                                </Col>
                                            )}
                                        {state.selectedInvRows.length > 0 &&
                                            state.mode == 'deleted' && (
                                                <Col>
                                                    <Button
                                                        danger
                                                        disabled={!editable}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            restoreSelected();
                                                        }}
                                                    >
                                                        Восстановить
                                                    </Button>
                                                </Col>
                                            )}
                                        <Col>
                                            <Checkbox
                                                checked={state.mode == 'deleted'}
                                                onClick={() => {
                                                    state.selectedInvRows = [];
                                                    state.mode =
                                                        state.mode == 'current'
                                                            ? 'deleted'
                                                            : 'current';
                                                }}
                                            >
                                                Корзина
                                            </Checkbox>
                                        </Col>
                                        <Col>
                                            {anyChanges ||
                                                (state.selectedInvRows.length == 0 && (
                                                    <Tooltip title="Для экспорта сохраните изменения и выберите приглашения">
                                                        <Button
                                                            className="export-btn"
                                                            disabled
                                                            icon={<div className="table-icon" />}
                                                        >
                                                            Экспорт
                                                        </Button>
                                                    </Tooltip>
                                                ))}
                                            {!anyChanges && state.selectedInvRows.length > 0 && (
                                                <Button
                                                    className="export-btn"
                                                    icon={<div className="table-icon" />}
                                                    onClick={exportInvitations}
                                                >
                                                    Экспорт
                                                </Button>
                                            )}
                                        </Col>
                                    </Row>
                                    <Row className="table-container">
                                        <Table<InviteDataType>
                                            bordered
                                            columns={columns as ColumnTypes}
                                            components={components}
                                            dataSource={makeTableData()}
                                            locale={{
                                                emptyText: (
                                                    <Col
                                                        className="empty-text p3"
                                                        flex={1}
                                                    >
                                                        <Row>Таких приглашений нет :)</Row>
                                                    </Col>
                                                ),
                                            }}
                                            pagination={false}
                                            rowClassName={() => 'editable-row'}
                                            rowSelection={invRowSelection}
                                            scroll={{
                                                x: 'max-content',
                                                y: 77 * 7,
                                            }}
                                        />
                                    </Row>
                                    <Row className="pagination-row">
                                        {state.invitationsMeta?.total != null &&
                                            state.page != state.invitationsMeta?.last_page && (
                                                <Button
                                                    className="p3"
                                                    onClick={loadMore}
                                                    type="primary"
                                                >
                                                    Показать ещё
                                                </Button>
                                            )}
                                        <Pagination
                                            align="end"
                                            current={state.page}
                                            pageSize={state.perPage}
                                            pageSizeOptions={[
                                                10,
                                                20,
                                                50,
                                                100,
                                            ]}
                                            total={state.invitationsMeta?.total ?? 0}
                                            onChange={onPageOrPerPageChange}
                                            showSizeChanger
                                        />
                                    </Row>
                                </Col>
                            </Row>
                            <Row className="p2 controls-row">
                                <Col>
                                    <Row>
                                        {anyChanges && (
                                            <Tooltip
                                                placement="topLeft"
                                                title={
                                                    countUnfinishedInvites() > 0
                                                        ? `Не заполненных приглашений: ${countUnfinishedInvites()}`
                                                        : null
                                                }
                                            >
                                                <Button
                                                    onClick={() => {
                                                        if (
                                                            anyChanges &&
                                                            countUnfinishedInvites() == 0
                                                        ) {
                                                            saveChanges();
                                                        }
                                                    }}
                                                >
                                                    Сохранить
                                                </Button>
                                            </Tooltip>
                                        )}
                                        {anyChanges && (
                                            <Popconfirm
                                                cancelText="Отмена"
                                                disabled={!anyChanges}
                                                okText="ОК"
                                                onConfirm={() => {
                                                    state.changes = {};
                                                    state.listMode = 'list';
                                                    loadInvitations();
                                                }}
                                                title="Не сохранённые изменения будут потеряны"
                                            >
                                                <Button>Отмена</Button>
                                            </Popconfirm>
                                        )}
                                    </Row>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                )}
            </div>
        </MainLayout>
    );
};

export default InvitationList;
