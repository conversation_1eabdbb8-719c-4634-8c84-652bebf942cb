import { TSession } from 'types/session/session';
import { useReactive } from 'ahooks';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button, Col, ConfigProvider, List, message, Pagination, Row, Tag } from 'antd';
import { CRMAPIManager } from '@api/crmApiManager';
import { SessionListResp } from '@api/responseModels/sessions/sessionListResponse';
import { useEffect } from 'react';
import MainLayout from '@components/ui/layout/mainLayout';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { Loader } from '@components/ui/loader/loader';
import { Permissions } from '@classes/permissions';
import { GlobalConstants } from '@classes/constants';
import { TMetadata } from 'types/api/metadata';
import { UserSelect } from '@components/ui/userSelect/userSelect';
import { TUser } from 'types/user/user';

import './sessionList.scss';

type TState = {
    isLoading: boolean;
    manager_id: TUser['id'] | null;
    page: number;
    perPage: number;
    sessions: TSession[];
    sessionsMeta: TMetadata | null;
};

const SessionList = (): JSX.Element => {
    const state = useReactive<TState>({
        isLoading: false,
        manager_id: null,
        page: 1,
        perPage: 10,
        sessions: [],
        sessionsMeta: null,
    });
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const [searchParams] = useSearchParams();

    async function loadSessions(page: number = state.page, addition: boolean = false) {
        state.isLoading = true;
        try {
            const sim_id = searchParams.get('sim_id');
            const result = await CRMAPIManager.request<SessionListResp>(async (api) => {
                return await api.getSessionList({
                    manager_id: state.manager_id,
                    simulation_id: sim_id != null ? +sim_id : null,
                    page: page,
                    per_page: state.perPage,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        created_at: null,
                    },
                });
            });
            if (result.errorMessages) throw result.errorMessages;
            state.sessions = addition ? [...state.sessions, ...result.data.data] : result.data.data;
            state.sessionsMeta = result.data.meta;
        } catch (err) {
            messageApi.error('Ошибка при загрузке списка сессий');
            console.log(err);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        state.manager_id = searchParams.get('manager_id');
        loadSessions();
    }, []);

    function makeListItems() {
        const arr = [];
        arr.push(...state.sessions);
        return arr;
    }

    async function onPageOrPerPageChange(page: number, pageSize: number) {
        if (page != state.page) {
            await loadSessions(page);
            state.page = page;
        }
        if (pageSize != state.perPage) {
            if (state.sessionsMeta?.total == null || state.sessionsMeta?.total == 0) return;
            state.perPage = pageSize;
            state.page = Math.ceil(state.sessionsMeta?.from_ / pageSize);
            await loadSessions();
        }
    }

    async function loadMore() {
        state.page += 1;
        await loadSessions(state.page, true);
    }

    return (
        <MainLayout
            activeTab="sessions"
            additionalClass="list-min-width"
            tabSet="controls"
        >
            <div className="session-list-container">
                {state.isLoading && <Loader />}
                {contextHolder}
                <div className="session-list">
                    <Row className="p3 tag-section">
                        <UserSelect
                            onSelect={(user: TUser['id'] | null) => {
                                state.manager_id = user;
                                state.page = 1;
                                loadSessions();
                            }}
                            placeholder={'-Выберите менеджера-'}
                            selected={state.manager_id}
                        />
                        {searchParams.get('sim_id') != null && (
                            <Tag
                                closeIcon
                                onClose={() => {
                                    searchParams.delete('sim_id');
                                    navigate('/controls/sessions');
                                    state.page = 1;
                                    loadSessions();
                                }}
                            >
                                ID симуляции {searchParams.get('sim_id')}
                            </Tag>
                        )}
                    </Row>
                    <ConfigProvider
                        theme={{
                            token: GlobalConstants.ListGridSettings,
                        }}
                    >
                        <List
                            dataSource={makeListItems()}
                            grid={GlobalConstants.ListGridCols}
                            itemLayout="horizontal"
                            loading={state.isLoading}
                            locale={{
                                emptyText: (
                                    <Col
                                        className="empty-text p3"
                                        flex={1}
                                    >
                                        <Row>Таких сессий нет :)</Row>
                                    </Col>
                                ),
                            }}
                            pagination={false}
                            renderItem={(item: TSession) => (
                                <div className="session-list-card">
                                    <Col flex={1}>
                                        <Row className="header-row">
                                            <div className="p2-strong">ID {item.id}</div>
                                            <CopyButton
                                                textToCopy={`ID ${item.id}`}
                                                textToShow="ID скопирован"
                                                size={24}
                                            />
                                        </Row>
                                        <Row className="body-row">
                                            <Col flex={1}>
                                                <Row className="p3">
                                                    <Col>
                                                        <span>Симуляция:</span>
                                                    </Col>
                                                    <Col>
                                                        <Row>
                                                            <Button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    navigate(
                                                                        `/simulations/${item.simulation_id}`,
                                                                    );
                                                                }}
                                                                type="link"
                                                            >
                                                                {item.simulation_name}
                                                            </Button>
                                                        </Row>
                                                    </Col>
                                                </Row>
                                                <Row className="p3">
                                                    <Col>
                                                        <span>Менеджер:</span>
                                                    </Col>
                                                    <Col>
                                                        <Row>
                                                            <Button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    navigate(
                                                                        `/management/users/${item.manager_id}`,
                                                                    );
                                                                }}
                                                                type="link"
                                                            >
                                                                {item.manager_name}
                                                            </Button>
                                                        </Row>
                                                    </Col>
                                                </Row>
                                            </Col>
                                        </Row>
                                        <Row className="event-card-controls p3">
                                            <Button
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    navigate(`/controls/sessions/${item.id}`);
                                                }}
                                            >
                                                Открыть
                                            </Button>
                                            {Permissions.checkPermission(
                                                Permissions.SessionAssignmentList,
                                            ) && (
                                                <Button
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        navigate(
                                                            `/controls/assignments?session_id=${item.id}`,
                                                        );
                                                    }}
                                                >
                                                    Назначения
                                                </Button>
                                            )}
                                        </Row>
                                    </Col>
                                </div>
                            )}
                        />
                    </ConfigProvider>
                    <Row className="pagination-row">
                        {state.sessionsMeta?.total != null &&
                            state.page != state.sessionsMeta?.last_page && (
                                <Button
                                    className="p3"
                                    onClick={loadMore}
                                    type="primary"
                                >
                                    Показать ещё
                                </Button>
                            )}
                        <Pagination
                            align="end"
                            current={state.page}
                            pageSize={state.perPage}
                            pageSizeOptions={[
                                10,
                                20,
                                50,
                                100,
                            ]}
                            total={state.sessionsMeta?.total ?? 0}
                            onChange={onPageOrPerPageChange}
                            showSizeChanger
                        />
                    </Row>
                </div>
            </div>
        </MainLayout>
    );
};

export default SessionList;
