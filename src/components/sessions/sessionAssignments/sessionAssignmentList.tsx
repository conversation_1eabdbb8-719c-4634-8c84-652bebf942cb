import MainLayout from '@components/ui/layout/mainLayout';
import { useReactive } from 'ahooks';
import { Button, Col, ConfigProvider, List, message, Pagination, Radio, Row, Tag } from 'antd';
import { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { TSessionAssignment } from 'types/session/sessionAssignment';
import { Permissions } from '@classes/permissions';
import { CRMAPIManager } from '@api/crmApiManager';
import { SessionAssignmentListResp } from '@api/responseModels/sessionAssignments/sessionAssignmentListResponse';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { rootStore } from '@store/instanse';
import { GlobalConstants } from '@classes/constants';
import { SettingsManager } from '@classes/settingsManager';
import { TMetadata } from 'types/api/metadata';

import './sessionAssignmentList.scss';

type TState = {
    assignments: TSessionAssignment[];
    assignmentsMeta: TMetadata | null;
    isLoading: boolean;
    page: number;
    perPage: number;
    status: 'prestarted' | 'started' | 'paused' | 'resumed' | 'stopped' | 'finished' | 'all';
};

const SessionAssignmentList = (): JSX.Element => {
    const state = useReactive<TState>({
        assignments: [],
        assignmentsMeta: null,
        isLoading: false,
        page: 1,
        perPage: 10,
        status: 'all',
    });
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const [searchParams] = useSearchParams();

    async function loadAssignments(page: number = state.page, addition: boolean = false) {
        state.isLoading = true;
        try {
            const session_id = searchParams.get('session_id');
            const user_id = searchParams.get('user_id');
            const result = await CRMAPIManager.request<SessionAssignmentListResp>(async (api) => {
                return await api.getSessionAssignmentList({
                    session_id: session_id,
                    user_id:
                        rootStore.currentUserStore.getUser?.role == 'Roles.Client'
                            ? SettingsManager.getConnectionCredentials()?.user_id
                            : user_id,
                    state: state.status == 'all' ? null : state.status,
                    page: page,
                    per_page: state.perPage,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        created_at: null,
                    },
                });
            });
            if (result.errorMessages) throw result.errorMessages;
            state.assignments = addition
                ? [...state.assignments, ...result.data.data]
                : result.data.data;
            state.assignmentsMeta = result.data.meta;
        } catch (err) {
            messageApi.error('Ошибка при загрузке списка назначений');
            console.log(err);
        }
        state.isLoading = false;
    }

    useEffect(() => {
        loadAssignments();
    }, [state.status]);

    function makeListItems() {
        const arr = [];
        arr.push(...state.assignments);
        return arr;
    }

    function stateToLocale(SA: TSessionAssignment) {
        switch (SA.state) {
            case 'prestarted':
                return 'Новое';
            case 'started':
                return 'Начато';
            case 'paused':
                return 'На паузе';
            case 'resumed':
                return 'Возобновлено';
            case 'stopped':
                return 'Остановлено';
            case 'finished':
                return 'Завершено';
            default:
                return SA.state;
        }
    }

    async function onPageOrPerPageChange(page: number, pageSize: number) {
        if (page != state.page) {
            await loadAssignments(page);
            state.page = page;
        }
        if (pageSize != state.perPage) {
            if (state.assignmentsMeta?.total == null || state.assignmentsMeta?.total == 0) return;
            state.perPage = pageSize;
            state.page = Math.ceil(state.assignmentsMeta?.from_ / pageSize);
            await loadAssignments();
        }
    }

    async function loadMore() {
        state.page += 1;
        await loadAssignments(state.page, true);
    }

    return (
        <MainLayout
            activeTab="assignments"
            additionalClass="list-min-width"
            tabSet="controls"
        >
            <div className="assignment-list-container">
                {contextHolder}
                <div
                    className={`radio-group${
                        searchParams.get('session_id') != null ||
                        searchParams.get('user_id') != null
                            ? ' and-tag'
                            : ''
                    }`}
                >
                    <Radio.Group
                        onChange={(e) => {
                            e.stopPropagation();
                            state.status = e.target.value;
                        }}
                        options={[
                            { label: 'Новые', value: 'prestarted' },
                            { label: 'Начатые', value: 'started' },
                            { label: 'На паузе', value: 'paused' },
                            { label: 'Возобновлённые', value: 'resumed' },
                            { label: 'Остановленные', value: 'stopped' },
                            { label: 'Завершённые', value: 'finished' },
                            { label: 'Все', value: 'all' },
                        ]}
                        optionType="button"
                        value={state.status}
                    />
                </div>
                <div className="assignment-list">
                    {(searchParams.get('session_id') != null ||
                        searchParams.get('user_id') != null) && (
                        <div className="p3 tag-section">
                            {searchParams.get('session_id') != null && (
                                <Tag
                                    closeIcon
                                    onClose={() => {
                                        searchParams.delete('session_id');
                                        navigate('/controls/assignments');
                                        loadAssignments();
                                    }}
                                >
                                    ID сессии {searchParams.get('session_id')}
                                </Tag>
                            )}
                            {searchParams.get('user_id') != null && (
                                <Tag
                                    closeIcon
                                    onClose={() => {
                                        searchParams.delete('user_id');
                                        navigate('/controls/assignments');
                                        loadAssignments();
                                    }}
                                >
                                    ID пользователя {searchParams.get('user_id')}
                                </Tag>
                            )}
                        </div>
                    )}
                    <ConfigProvider
                        theme={{
                            token: GlobalConstants.ListGridSettings,
                        }}
                    >
                        <List
                            dataSource={makeListItems()}
                            grid={GlobalConstants.ListGridCols}
                            itemLayout="horizontal"
                            loading={state.isLoading}
                            locale={{
                                emptyText: (
                                    <Col
                                        className="empty-text p3"
                                        flex={1}
                                    >
                                        <Row>Таких назначений нет :)</Row>
                                    </Col>
                                ),
                            }}
                            pagination={false}
                            renderItem={(item: TSessionAssignment) => (
                                <div
                                    className={`assignment-list-card${
                                        Permissions.checkPermission(Permissions.SessionGet)
                                            ? ''
                                            : ' shorter'
                                    }`}
                                >
                                    <Col flex={1}>
                                        <Row className="header-row">
                                            <div className="p2-strong">ID {item.id}</div>
                                            <CopyButton
                                                textToCopy={`ID ${item.id}`}
                                                textToShow="ID скопирован"
                                                size={24}
                                            />
                                        </Row>
                                        <Row className="body-row">
                                            <Col flex={1}>
                                                {Permissions.checkPermission(
                                                    Permissions.SessionGet,
                                                ) && (
                                                    <Row className="p3">
                                                        <Col>
                                                            <span>Сессия (ID):</span>
                                                        </Col>
                                                        <Col>
                                                            <Row>
                                                                <Button
                                                                    disabled={
                                                                        !Permissions.checkPermission(
                                                                            Permissions.SessionGet,
                                                                        )
                                                                    }
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        navigate(
                                                                            `/controls/sessions/${item.session_id}`,
                                                                        );
                                                                    }}
                                                                    type="link"
                                                                >
                                                                    {item.session_id}
                                                                </Button>
                                                            </Row>
                                                        </Col>
                                                    </Row>
                                                )}
                                                <Row className="p3">
                                                    <Col>
                                                        <span>Симуляция:</span>
                                                    </Col>
                                                    <Col>
                                                        <span>{item.simulation_name}</span>
                                                    </Col>
                                                </Row>
                                                <Row className="p3">
                                                    <Col>
                                                        <span>Менеджер:</span>
                                                    </Col>
                                                    <Col>
                                                        <Row>
                                                            <Button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    navigate(
                                                                        `/management/users/${item.manager_id}`,
                                                                    );
                                                                }}
                                                                type="link"
                                                            >
                                                                {item.manager_name}
                                                            </Button>
                                                        </Row>
                                                    </Col>
                                                </Row>
                                                <Row className="p3">
                                                    <Col>
                                                        <span>Пользователь:</span>
                                                    </Col>
                                                    <Col>
                                                        <Row>
                                                            <Button
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    navigate(
                                                                        `/management/users/${item.user_id}`,
                                                                    );
                                                                }}
                                                                type="link"
                                                            >
                                                                {item.user_name}
                                                            </Button>
                                                        </Row>
                                                    </Col>
                                                </Row>
                                                <Row className="p3">
                                                    <span>Состояние:</span>
                                                    <span>{stateToLocale(item)}</span>
                                                </Row>
                                                <Row className="p3">
                                                    <span>Время:</span>
                                                    <span>
                                                        {rootStore.ingameStore.dayTickToString(
                                                            item,
                                                        )}
                                                    </span>
                                                </Row>
                                                <Row className="event-card-controls p3">
                                                    <Button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            navigate(
                                                                `/controls/assignments/${item.id}`,
                                                            );
                                                        }}
                                                    >
                                                        Открыть
                                                    </Button>
                                                </Row>
                                            </Col>
                                        </Row>
                                    </Col>
                                </div>
                            )}
                        />
                    </ConfigProvider>
                    <Row className="pagination-row">
                        {state.assignmentsMeta?.total != null &&
                            state.page != state.assignmentsMeta?.last_page && (
                                <Button
                                    className="p3"
                                    onClick={loadMore}
                                    type="primary"
                                >
                                    Показать ещё
                                </Button>
                            )}
                        <Pagination
                            align="end"
                            current={state.page}
                            pageSize={state.perPage}
                            pageSizeOptions={[
                                10,
                                20,
                                50,
                                100,
                            ]}
                            total={state.assignmentsMeta?.total ?? 0}
                            onChange={onPageOrPerPageChange}
                            showSizeChanger
                        />
                    </Row>
                </div>
            </div>
        </MainLayout>
    );
};

export default SessionAssignmentList;
