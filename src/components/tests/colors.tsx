import Colors from '@classes/colors';
import { Common } from '@classes/common';
import MainLayout from '@components/ui/layout/mainLayout';
import { Col, Row } from 'antd';

export function prepareGrid(): Array<{
    group: string;
    colors: Array<{
        title: string;
        hex: string;
    }>;
}> {
    const grid = [];
    const groups = Object.keys(Colors);
    for (let i = 0; i < groups.length; i++) {
        const groupInner = Object.keys(Colors[groups[i]]);
        if (groupInner.includes('warm')) {
            for (let j = 0; j < groupInner.length; j++) {
                grid.push({
                    group: `${groups[i]}.${groupInner[j]}`,
                    colors: Object.keys(Colors[groups[i]][groupInner[j]]).map((v) => {
                        return { title: v, hex: Colors[groups[i]][groupInner[j]][v] };
                    }),
                });
            }
        } else {
            grid.push({
                group: `${groups[i]}`,
                colors: Object.keys(Colors[groups[i]]).map((v) => {
                    return { title: v, hex: Colors[groups[i]][v] };
                }),
            });
        }
    }
    return grid;
}

const ColorsTest = (): JSX.Element => {
    return (
        <MainLayout
            activeTab="colors"
            tabSet="dev"
        >
            <div
                style={{
                    background: Colors.Accent.warm[0],
                    border: `2px solid ${Colors.Neutral[100]}`,
                    borderRadius: '4px',
                    width: '100%',
                }}
            >
                <div
                    style={{
                        padding: '40px 40px 40px 38px',
                        width: '100%',
                    }}
                >
                    <Row style={{ columnGap: '8px' }}>
                        {prepareGrid().map((col) => (
                            <Col
                                key={`${col.group}`}
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    rowGap: '8px',
                                }}
                            >
                                <Row
                                    key={col.group}
                                    className="p2"
                                    gutter={[8, 8]}
                                >
                                    {col.group}
                                </Row>
                                {col.colors.map((row) => (
                                    <Row
                                        key={`${col.group}-${row.title}`}
                                        onClick={() => {
                                            Common.clipboardCopy(row.hex, row.hex);
                                        }}
                                        style={{
                                            alignItems: 'center',
                                            columnGap: '8px',
                                        }}
                                    >
                                        <Col>
                                            <div
                                                style={{
                                                    backgroundColor: row.hex,
                                                    border: `1px solid ${Colors.Neutral[25]}`,
                                                    borderRadius: '6px',
                                                    height: '32px',
                                                    width: '32px',
                                                }}
                                            >
                                                &nbsp;
                                            </div>
                                        </Col>
                                        <Col className="p3">{`${col.group}-${row.title}`}</Col>
                                    </Row>
                                ))}
                            </Col>
                        ))}
                    </Row>
                </div>
            </div>
        </MainLayout>
    );
};

export default ColorsTest;
