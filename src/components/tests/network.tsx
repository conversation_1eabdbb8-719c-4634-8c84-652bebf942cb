import { CRMAPIManager } from '@api/crmApiManager';
import { ChatListResp } from '@api/responseModels/chat/chatListResponse';
import { ChatMessageListResp } from '@api/responseModels/chatMessage/chatMessageListResponse';
import { ChatUserListResp } from '@api/responseModels/chatUser/chatUserListResponse';
import { FilterListResp } from '@api/responseModels/filters/filterListResponse';
import { InvitationListResp } from '@api/responseModels/invitations/invitationListResponse';
import { NotificationListResp } from '@api/responseModels/notifications/notificationListResponse';
import { PermissionCategoryListResp } from '@api/responseModels/permissions/permissionCategoryListResponse';
import { PermissionListResp } from '@api/responseModels/permissions/permissionListResponse';
import { RoleListResp } from '@api/responseModels/roles/roleListResponse';
import { SessionAssignmentListResp } from '@api/responseModels/sessionAssignments/sessionAssignmentListResponse';
import { SessionListResp } from '@api/responseModels/sessions/sessionListResponse';
import { SessionWorkerAssignmentListResp } from '@api/responseModels/sessionWorkerAssignments/sessionWorkerAssignmentListResponse';
import { SimEventListResp } from '@api/responseModels/simulations/simulationEvents/simulationEventListResponse';
import { SimulationListResp } from '@api/responseModels/simulations/simulationListResponse';
import { SimTaskListResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskListResponse';
import { SimWorkerListResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerListResponse';
import { UserListResp } from '@api/responseModels/users/userListResp';
import Colors from '@classes/colors';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { Loader } from '@components/ui/loader/loader';
import MainLayout from '@components/ui/layout/mainLayout';
import { faGear, faPoop, faUserXmark } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useReactive } from 'ahooks';
import { Button, Col, Radio, Row } from 'antd';
import { useEffect } from 'react';

import './tests.scss';

type TGroupName =
    | 'invitations'
    | 'users'
    | 'simulations'
    | 'simulation-tasks'
    | 'simulation-workers'
    | 'simulation-events'
    | 'sessions'
    | 'session-assignments'
    | 'session-worker-assignments'
    | 'chats'
    | 'user-chats'
    | 'chat-messages'
    | 'notifications'
    | 'roles'
    | 'permission-categories'
    | 'permissions'
    | 'filters';

type TState = {
    isLoading: boolean;
    output: any;
    radioValue: TGroupName;
    results: { [key in TGroupName]: { pass: 'good' | 'err' | 'no-parent'; result: any } } | null;
};

const NetworkTests = (): JSX.Element => {
    const defResults: TState['results'] = {
        invitations: { pass: 'err', result: 'Пусто' },
        users: { pass: 'err', result: 'Пусто' },
        simulations: { pass: 'err', result: 'Пусто' },
        'simulation-tasks': { pass: 'err', result: 'Пусто' },
        'simulation-workers': { pass: 'err', result: 'Пусто' },
        'simulation-events': { pass: 'err', result: 'Пусто' },
        sessions: { pass: 'err', result: 'Пусто' },
        'session-assignments': { pass: 'err', result: 'Пусто' },
        'session-worker-assignments': { pass: 'err', result: 'Пусто' },
        chats: { pass: 'err', result: 'Пусто' },
        'user-chats': { pass: 'err', result: 'Пусто' },
        'chat-messages': { pass: 'err', result: 'Пусто' },
        notifications: { pass: 'err', result: 'Пусто' },
        roles: { pass: 'err', result: 'Пусто' },
        'permission-categories': { pass: 'err', result: 'Пусто' },
        permissions: { pass: 'err', result: 'Пусто' },
        filters: { pass: 'err', result: 'Пусто' },
    };
    const state = useReactive<TState>({
        isLoading: false,
        output: 'Пусто',
        radioValue: 'invitations',
        results: defResults,
    });

    async function loadInvitations() {
        try {
            const result = await CRMAPIManager.request<InvitationListResp>(async (api) => {
                return await api.getInvitationList({
                    status: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        sender: null,
                        created_at: null,
                        deleted: 'all',
                    },
                });
            });
            if (result.errorMessages) throw result;
            state.results['invitations'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['invitations'] = { pass: 'err', result: err };
        }
    }

    async function loadUsers() {
        try {
            const result = await CRMAPIManager.request<UserListResp>(async (api) => {
                return await api.getUserList({
                    query: null,
                    role: null,
                    per_page: 100,
                    page: 1,
                });
            });
            if (result.errorMessages) throw result;
            state.results['users'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['users'] = { pass: 'err', result: err };
        }
    }

    async function loadSimulations() {
        try {
            const result = await CRMAPIManager.request<SimulationListResp>(async (api) => {
                return await api.getSimulationList({
                    query: null,
                    status: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        creator: null,
                        created_at: null,
                        deleted: 'all',
                    },
                });
            });
            if (result.errorMessages) throw result;
            state.results['simulations'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['simulations'] = { pass: 'err', result: err };
        }
    }

    async function loadSimulationTasks() {
        const simResult = state.results['simulations'].result;
        if (simResult?.data == undefined || simResult.data.data.length == 0) {
            state.results['simulation-tasks'] = { pass: 'no-parent', result: 'Нет родителя' };
            return;
        }
        try {
            const result = await CRMAPIManager.request<SimTaskListResp>(async (api) => {
                return await api.getSimTaskList({
                    simulation_id: simResult.data.data[0].id,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'all',
                    },
                });
            });
            if (result.errorMessages) throw result;
            state.results['simulation-tasks'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['simulation-tasks'] = { pass: 'err', result: err };
        }
    }

    async function loadSimulationWorkers() {
        const simResult = state.results['simulations'].result;
        if (simResult?.data == undefined || simResult.data.data.length == 0) {
            state.results['simulation-workers'] = { pass: 'no-parent', result: 'Нет родителя' };
            return;
        }
        try {
            const result = await CRMAPIManager.request<SimWorkerListResp>(async (api) => {
                return await api.getSimWorkerList({
                    simulation_id: simResult.data.data[0].id,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'all',
                    },
                });
            });
            if (result.errorMessages) throw result;
            state.results['simulation-workers'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['simulation-workers'] = { pass: 'err', result: err };
        }
    }

    async function loadSimulationEvents() {
        const simResult = state.results['simulations'].result;
        if (simResult?.data == undefined || simResult.data.data.length == 0) {
            state.results['simulation-events'] = { pass: 'no-parent', result: 'Нет родителя' };
            return;
        }
        try {
            const result = await CRMAPIManager.request<SimEventListResp>(async (api) => {
                return await api.getSimEventList({
                    query: null,
                    simulation_id: simResult.data.data[0].id,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'all',
                    },
                });
            });
            if (result.errorMessages) throw result;
            state.results['simulation-events'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['simulation-events'] = { pass: 'err', result: err };
        }
    }

    async function loadSessions() {
        try {
            const result = await CRMAPIManager.request<SessionListResp>(async (api) => {
                return await api.getSessionList({
                    manager_id: null,
                    simulation_id: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        created_at: null,
                    },
                });
            });
            if (result.errorMessages) throw result;
            state.results['sessions'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['sessions'] = { pass: 'err', result: err };
        }
    }

    async function loadSessionAssignments() {
        try {
            const result = await CRMAPIManager.request<SessionAssignmentListResp>(async (api) => {
                return await api.getSessionAssignmentList({
                    session_id: null,
                    user_id: null,
                    state: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        created_at: null,
                    },
                });
            });
            if (result.errorMessages) throw result;
            state.results['session-assignments'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['session-assignments'] = { pass: 'err', result: err };
        }
    }

    async function loadSWA() {
        const SAresult = state.results['session-assignments'].result;
        if (SAresult?.data == undefined || SAresult.data.data.length == 0) {
            state.results['session-worker-assignments'] = {
                pass: 'no-parent',
                result: 'Нет родителя',
            };
            return;
        }
        try {
            const result = await CRMAPIManager.request<SessionWorkerAssignmentListResp>(
                async (api) => {
                    return await api.getSessionWorkerAssignmentList({
                        session_assignment_id: SAresult.data.data[0].id,
                        day: null,
                        page: 1,
                        per_page: 100,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            created_at: null,
                        },
                    });
                },
            );
            if (result.errorMessages) throw result;
            state.results['session-worker-assignments'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['session-worker-assignments'] = { pass: 'err', result: err };
        }
    }

    async function loadChats() {
        try {
            const result = await CRMAPIManager.request<ChatListResp>(async (api) => {
                return await api.getChatList({
                    user_id: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'null',
                    },
                });
            });
            if (result.errorMessages) throw result;
            state.results['chats'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['chats'] = { pass: 'err', result: err };
        }
    }

    async function loadUserChats() {
        try {
            const result = await CRMAPIManager.request<ChatUserListResp>(async (api) => {
                return await api.getChatUserList({
                    user_id: null,
                    chat_id: null,
                    page: 1,
                    per_page: 100,
                });
            });
            if (result.errorMessages) throw result;
            state.results['user-chats'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['user-chats'] = { pass: 'err', result: err };
        }
    }

    async function loadChatMessages() {
        const chatResult = state.results['chat-messages'].result;
        if (chatResult?.data == undefined || chatResult.data.data.length == 0) {
            state.results['chat-messages'] = { pass: 'no-parent', result: 'Нет родителя' };
            return;
        }
        try {
            const result = await CRMAPIManager.request<ChatMessageListResp>(async (api) => {
                return await api.getChatMessageList({
                    chat_id: chatResult.data.data[0].id,
                    query: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    count: null,
                    from_id: null,
                });
            });
            if (result.errorMessages) throw result;
            state.results['chat-messages'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['chat-messages'] = { pass: 'err', result: err };
        }
    }

    async function loadNotifications() {
        try {
            const result = await CRMAPIManager.request<NotificationListResp>(async (api) => {
                return await api.getNotificationList({
                    user_id: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                });
            });
            if (result.errorMessages) throw result;
            state.results['notifications'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['notifications'] = { pass: 'err', result: err };
        }
    }

    async function loadRoles() {
        try {
            const result = await CRMAPIManager.request<RoleListResp>(async (api) => {
                return await api.getRoleList();
            });
            if (result.errorMessages) throw result;
            state.results['roles'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['roles'] = { pass: 'err', result: err };
        }
    }

    async function loadPermissionCategories() {
        try {
            const result = await CRMAPIManager.request<PermissionCategoryListResp>(async (api) => {
                return await api.getPermissionCategoryList({
                    category_name: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                });
            });
            if (result.errorMessages) throw result;
            state.results['permission-categories'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['permission-categories'] = { pass: 'err', result: err };
        }
    }

    async function loadPermissions() {
        try {
            const result = await CRMAPIManager.request<PermissionListResp>(async (api) => {
                return await api.getPermissionList({
                    category_name: null,
                    page: 1,
                    per_page: 200,
                    sort_by: null,
                    sort_direction: null,
                });
            });
            if (result.errorMessages) throw result;
            state.results['permissions'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['permissions'] = { pass: 'err', result: err };
        }
    }

    async function loadFilters() {
        try {
            const result = await CRMAPIManager.request<FilterListResp>(async (api) => {
                return await api.getFilterList({
                    query: null,
                    page: 1,
                    per_page: 100,
                    sort_by: null,
                    sort_direction: null,
                    filters: {
                        deleted: 'all',
                    },
                });
            });
            if (result.errorMessages) throw result;
            state.results['filters'] = { pass: 'good', result: result };
        } catch (err) {
            state.results['filters'] = { pass: 'err', result: err };
        }
    }

    async function init() {
        state.isLoading = true;
        state.results = defResults;
        await loadInvitations();
        await loadUsers();
        await loadSimulations();
        await loadSimulationTasks();
        await loadSimulationWorkers();
        await loadSimulationEvents();
        await loadSessions();
        await loadSessionAssignments();
        await loadSWA();
        await loadChats();
        await loadUserChats();
        await loadChatMessages();
        await loadNotifications();
        await loadRoles();
        await loadPermissionCategories();
        await loadPermissions();
        await loadFilters();
        state.output = state.results[state.radioValue].result;
        state.isLoading = false;
    }

    async function reloadSelected() {
        state.isLoading = true;
        switch (state.radioValue) {
            case 'invitations': {
                await loadInvitations();
                break;
            }
            case 'users': {
                await loadUsers();
                break;
            }
            case 'simulations': {
                await loadSimulations();
                break;
            }
            case 'simulation-tasks': {
                await loadSimulationTasks();
                break;
            }
            case 'simulation-workers': {
                await loadSimulationWorkers();
                break;
            }
            case 'simulation-events': {
                await loadSimulationEvents();
                break;
            }
            case 'sessions': {
                await loadSessions();
                break;
            }
            case 'session-assignments': {
                await loadSessionAssignments();
                break;
            }
            case 'session-worker-assignments': {
                await loadSWA();
                break;
            }
            case 'chats': {
                await loadChats();
                break;
            }
            case 'user-chats': {
                await loadUserChats();
                break;
            }
            case 'chat-messages': {
                await loadChatMessages();
                break;
            }
            case 'notifications': {
                await loadNotifications();
                break;
            }
            case 'roles': {
                await loadRoles();
                break;
            }
            case 'permission-categories': {
                await loadPermissionCategories();
                break;
            }
            case 'permissions': {
                await loadPermissions();
                break;
            }
            case 'filters': {
                await loadFilters();
                break;
            }
        }
        state.isLoading = false;
    }

    useEffect(() => {
        init();
    }, []);

    function radioChange(value: TGroupName) {
        state.radioValue = value;
        state.output = state.results[value].result;
    }

    return (
        <MainLayout
            activeTab="network"
            tabSet="dev"
        >
            <div className="network-container">
                {state.isLoading && <Loader />}
                <div className="network-inner">
                    <Col>
                        <Radio.Group
                            className="group-selector p3"
                            onChange={(e) => radioChange(e.target.value)}
                            options={Object.keys(defResults).map((v) => {
                                return {
                                    value: v,
                                    label: (
                                        <>
                                            {v}{' '}
                                            {state.results[v].pass == 'good' ? (
                                                <FontAwesomeIcon
                                                    color={Colors.Success.warm[800]}
                                                    icon={faGear}
                                                />
                                            ) : state.results[v].pass == 'no-parent' ? (
                                                <FontAwesomeIcon
                                                    color={Colors.Warning.warm[800]}
                                                    icon={faUserXmark}
                                                />
                                            ) : (
                                                <FontAwesomeIcon
                                                    color={Colors.Error.warm[800]}
                                                    icon={faPoop}
                                                />
                                            )}
                                        </>
                                    ),
                                };
                            })}
                            value={state.radioValue}
                        />
                    </Col>
                    <Col className="fit-height desc-l">
                        <pre>
                            {state.output instanceof Error
                                ? JSON.stringify(
                                      state.output,
                                      Object.getOwnPropertyNames(state.output),
                                      4,
                                  )
                                : JSON.stringify(state.output, null, 4)}
                        </pre>
                    </Col>
                    <Col className="store-controls p2">
                        <Row>
                            <Button
                                onClick={() => {
                                    init();
                                }}
                                type="primary"
                            >
                                Обновить все
                            </Button>
                        </Row>
                        <Row>
                            <Button
                                onClick={() => {
                                    reloadSelected();
                                }}
                                type="primary"
                            >
                                Обновить выбранное
                            </Button>
                        </Row>
                        <Row>
                            <CopyButton
                                textToCopy={JSON.stringify(state.output, null, 4)}
                                textToShow={`${state.radioValue} скопировано`}
                                size={32}
                            />
                        </Row>
                    </Col>
                </div>
            </div>
        </MainLayout>
    );
};

export default NetworkTests;
