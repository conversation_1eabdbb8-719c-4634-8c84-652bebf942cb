import { useLocation, Navigate, Outlet } from 'react-router-dom';
import { Permissions } from '@classes/permissions';
import { message } from 'antd';

interface RequireAuthProps {
    requiredPermissions: Permissions[];
    children?: React.ReactNode;
}

export const ProtectedRoute = ({ requiredPermissions }: RequireAuthProps) => {
    const hasRequiredPermissions = requiredPermissions.every((permission) =>
        Permissions.checkPermission(String(permission)),
    );

    if (!hasRequiredPermissions) {
        message.error('У вас недостаточно прав для доступа к данной странице');
        return (
            <Navigate
                to="/lk"
                replace
            />
        );
    }

    return <Outlet />;
};
