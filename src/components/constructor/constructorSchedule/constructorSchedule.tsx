import { CRMAPIManager } from '@api/crmApiManager';
import { ScheduleEventTypeListResp } from '@api/responseModels/scheduleEventType/scheduleEventTypeListResponse';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { SimScheduleEventListResp } from '@api/responseModels/simulations/simulationScheduleEvents/simulationScheduleEventListResponse';
import { SimWorkerListResp } from '@api/responseModels/simulations/simulationWorkers/simulationWorkerListResponse';
import Colors from '@classes/colors';
import { GlobalConstants } from '@classes/constants';
import { Permissions } from '@classes/permissions';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import { Loader } from '@components/ui/loader/loader';
import MainLayout from '@components/ui/layout/mainLayout';
import { useReactive } from 'ahooks';
import { Button, Checkbox, Col, ConfigProvider, List, message, Radio, Row } from 'antd';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimulation } from 'types/simulation/simulation';
import { TScheduleEventType, TSimScheduleEvent } from 'types/simulation/simulationScheduleEvent';
import { TSimWorker } from 'types/simulation/simulationWorker';
import { ScheduleEventTypeModal } from './scheduleEventTypeModal';
import { Common } from '@classes/common';
import _ from 'lodash';
import { Schedule } from '@components/schedule/schedule';
import { DefaultTimeSettings } from '@store/ingame/data';
import { ScheduleEventTypeResp } from '@api/responseModels/scheduleEventType/scheduleEventTypeResponse';
import { TBulkUpdate } from 'types/api/bulkUpdate';
import { SimScheduleEventResp } from '@api/responseModels/simulations/simulationScheduleEvents/simulationScheduleEventResponse';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';
import { ScheduleEventTypeListParams } from 'types/api/listParams';

import './constructorSchedule.scss';

export enum ConstructorScheduleUseCases {
    SimScheduleEvents = 'schedule-events',
    ScheduleEventTypes = 'schedule-event-types',
}

type TProps = {
    useCase: ConstructorScheduleUseCases;
};

type TScheduleChange = TSimScheduleEvent & {
    change_type: 'add' | 'update' | 'remove';
};

type TState = {
    changes: { [key: TSimScheduleEvent['id']]: TScheduleChange };
    isLoading: boolean;
    lastSSETid: number;
    mode: 'current' | 'deleted' | 'all';
    scheduleEventList: TSimScheduleEvent[];
    scheduleEventListMode: 'current' | 'deleted' | 'all';
    scheduleEventTypeList: TScheduleEventType[];
    scheduleWeekSelected: number;
    selectedSET: TScheduleEventType | null;
    SETmodalOpen: boolean;
    SETmodalUseCase: 'new' | 'edit';
    simulation: TSimulation | null;
    simulationOriginal: TSimulation | null;
    workers: TSimWorker[];
};

const ConstructorSchedule = ({ useCase }: TProps): JSX.Element => {
    const state = useReactive<TState>({
        changes: {},
        isLoading: false,
        lastSSETid: 0,
        mode: 'current',
        scheduleEventList: [],
        scheduleEventListMode: 'current',
        scheduleEventTypeList: [],
        scheduleWeekSelected: 1,
        selectedSET: null,
        SETmodalOpen: false,
        SETmodalUseCase: 'new',
        simulation: null,
        simulationOriginal: null,
        workers: [],
    });
    const { simId } = useParams();
    const [messageApi, contextHolder] = message.useMessage();
    const navigate = useNavigate();
    const disableEdit =
        state.simulation?.archived ||
        state.simulation?.deleted_at != null ||
        state.simulation?.published ||
        state.simulation?.finished;
    const anySETchanges = !_.isEqual(
        state.simulation?.allowed_schedule_event_types,
        state.simulationOriginal?.allowed_schedule_event_types,
    );
    const anySSETchanges = Object.values(state.changes).length > 0;

    async function loadParentSim() {
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            if (sim.errorMessages) throw sim.errorMessages;
            state.simulation = { ...sim.data.data, allowed_schedule_event_types: [] };
            state.simulationOriginal = { ...sim.data.data, allowed_schedule_event_types: [] };
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/simulations');
            }
            messageApi.error('Ошибка при загрузке симуляции!');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadWorkerList() {
        state.isLoading = true;
        try {
            const wl = await CRMAPIManager.getAll<SimWorkerListResp>(
                async (api, page, per_page) => {
                    return await api.getSimWorkerList({
                        simulation_id: +simId,
                        page: page,
                        per_page: per_page,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            deleted: 'null',
                        },
                    });
                },
            );
            if (wl.errorMessages) throw wl.errorMessages;
            state.workers = wl.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при загрузке ПШЕ!');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadScheduleEventList() {
        //state.scheduleEventList = [];
        //state.lastSSETid = 0;
        state.isLoading = true;
        try {
            const sel = await CRMAPIManager.request<SimScheduleEventListResp>(async (api) => {
                return await api.getSimScheduleEventList({
                    simulation_id: +simId,
                    week: null,
                });
            });
            if (sel.errorMessages) throw sel.errorMessages;
            state.scheduleEventList = sel.data.data;
            state.lastSSETid = 0;
            for (let i = 0; i < sel.data.data.length; i++) {
                if (sel.data.data[i].id > state.lastSSETid) {
                    state.lastSSETid = sel.data.data[i].id;
                }
            }
        } catch (errors) {
            if (errors?.includes('Объект не найден')) {
                state.scheduleEventList = [];
                state.lastSSETid = 0;
            } else {
                messageApi.error('Ошибка при загрузке календаря симуляции!');
                console.log(errors);
            }
        }
        state.isLoading = false;
    }

    async function loadScheduleEventTypeList() {
        //state.scheduleEventTypeList = [];
        state.isLoading = true;
        try {
            const setl = await CRMAPIManager.getAll<ScheduleEventTypeListResp>(
                async (api, page, per_page) => {
                    return await api.getScheduleEventTypeList({
                        query: null,
                        max_start: null,
                        min_start: null,
                        page: page,
                        per_page: per_page,
                        sort_by: null,
                        sort_direction: null,
                        filters: {
                            deleted: {
                                all: 'all',
                                current: 'null',
                                deleted: 'only',
                            }[state.mode] as ScheduleEventTypeListParams['filters']['deleted'],
                        },
                    });
                },
            );
            if (setl.errorMessages) throw setl.errorMessages;
            state.scheduleEventTypeList = setl.data.data;
        } catch (errors) {
            messageApi.error('Ошибка при загрузке типов календарных сущностей!');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadNecessary() {
        state.changes = {};
        state.SETmodalOpen = false;
        state.selectedSET = null;
        state.SETmodalUseCase = 'new';

        if (state.simulation == null) {
            await loadParentSim();
        }
        if (state.simulation == null) return;
        if (state.workers.length == 0) {
            await loadWorkerList();
        }
        if (state.scheduleEventTypeList.length == 0) {
            await loadScheduleEventTypeList();
        }
        if (useCase == ConstructorScheduleUseCases.SimScheduleEvents) {
            await loadScheduleEventList();
        }
    }

    useEffect(() => {
        loadNecessary();
    }, [useCase]);

    useEffect(() => {
        loadScheduleEventTypeList();
    }, [state.mode]);

    function makeSETListItems() {
        const arr = [];
        if (state.scheduleEventListMode == 'current' && !disableEdit) {
            arr.push('add');
        }
        arr.push(...state.scheduleEventTypeList);
        return arr;
    }

    function onSETaddClick() {
        const defaultSET: TScheduleEventType = {
            id: state.lastSSETid + 1,
            event_level: 'global',
            name: '',
            picture: null,
            duration: 1,
            min_start: 8,
            max_start: 19,
            only_on_weekday: [
                1,
                2,
                3,
                4,
                5,
            ],
            cooldown_type: null,
            cooldown_size: null,
            prevent_progress: false,
            effects: [],
            overflow_days: null,
            overflow_count: null,
            overflow_effects: [],
            created_at: Common.dateNowString(),
            updated_at: Common.dateNowString(),
            deleted_at: null,
        };
        state.selectedSET = defaultSET;
        state.SETmodalUseCase = 'new';
        state.SETmodalOpen = true;
        state.lastSSETid += 1;
    }

    function onSETeditClick(item: TScheduleEventType) {
        state.selectedSET = item;
        state.SETmodalUseCase = 'edit';
        state.SETmodalOpen = true;
    }

    function makeCooldownString(SET: TScheduleEventType) {
        if (SET.cooldown_type == null || SET.cooldown_size == null) {
            return '-';
        }
        return SET.cooldown_size == 0
            ? `1 в ${SET.cooldown_type == 'week' ? 'нед.' : 'д.'}`
            : `+${SET.cooldown_size} ${SET.cooldown_type == 'week' ? 'нед.' : 'дн.'}`;
    }

    function makeOverflowString(SET: TScheduleEventType) {
        if (SET.overflow_days == null || SET.overflow_count == null) {
            return '-';
        }
        return `${SET.overflow_count} шт./${SET.overflow_days} дн./${SET.overflow_effects.length} эфф.`;
    }

    function setSETmodalOpen(isOpen: boolean) {
        if (!isOpen) {
            state.SETmodalOpen = false;
            state.selectedSET = null;
        }
    }

    async function onSETdelete(schedule_event_type_id: TScheduleEventType['id']) {
        /*
        state.scheduleEventTypeList = state.scheduleEventTypeList.filter(SETi => 
            SETi.id != schedule_event_type_id
        );
        console.log(state.scheduleEventTypeList);
        state.SETmodalOpen = false;
        state.selectedSET = null;
        */
        state.isLoading = true;
        try {
            const tSET = await CRMAPIManager.request<ScheduleEventTypeResp>(async (api) => {
                return await api.removeScheduleEventType(schedule_event_type_id);
            });
            if (tSET.errorMessages) throw tSET.errorMessages;
            await loadScheduleEventTypeList();
            state.SETmodalOpen = false;
            state.selectedSET = null;
            messageApi.success('Тип события удалён');
        } catch (errors) {
            messageApi.error('Ошибка при удалении типа события');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function validateSET(): boolean {
        if (Common.isNullOrEmptyString(state.selectedSET?.name)) {
            messageApi.warning('Укажите имя типу события');
            return false;
        }
        if (state.selectedSET?.duration < 1 || state.selectedSET?.duration > 12) {
            messageApi.warning('Продолжительность должна быть от 1 до 12 часов');
            return false;
        }
        if (state.selectedSET?.min_start + state.selectedSET?.duration > 20) {
            messageApi.warning(
                'При указанных мин. часе старта и длительности событие выйдет за границы дня',
            );
            return false;
        }
        if (state.selectedSET?.max_start + state.selectedSET?.duration > 20) {
            messageApi.warning(
                'При указанных макс. часе старта и длительности событие выйдет за границы дня',
            );
            return false;
        }
        if (state.selectedSET?.only_on_weekday.length == 0) {
            messageApi.warning('Укажите хотя бы один доступный день недели');
            return false;
        }
        if (state.selectedSET?.effects.length == 0) {
            messageApi.warning('Добавьте типу события хотя бы один эффект');
            return false;
        }
        if (
            state.selectedSET?.cooldown_type != null &&
            (state.selectedSET?.cooldown_size == null || state.selectedSET?.cooldown_size > 100)
        ) {
            messageApi.warning(
                'Укажите период ограничения повторного использования - от 0 до 100 выбранных единиц',
            );
            return false;
        }
        if (
            state.selectedSET?.overflow_days != null &&
            (state.selectedSET?.overflow_count == null || state.selectedSET?.overflow_count > 100)
        ) {
            messageApi.warning('Укажите допустимое кол-во использований в периоде - от 1 до 100');
            return false;
        }
        if (
            state.selectedSET?.overflow_days != null &&
            state.selectedSET?.overflow_count != null &&
            state.selectedSET?.overflow_effects.length == 0
        ) {
            messageApi.warning('Добавьте в санкциях злоупотребления хотя бы один эффект');
            return false;
        }
        const targetParamList = [
            'global_motivation',
            'global_productivity',
            'global_hours_muda',
            'global_hours_review',
            'worker_motivation',
            'worker_productivity',
        ];
        for (let i = 0; i < targetParamList.length; i++) {
            const curTargetParam = targetParamList[i];
            const targetParamEffects = state.selectedSET.effects.filter(
                (effi) => effi.target_param == curTargetParam,
            );
            if (targetParamEffects.length >= 2) {
                messageApi.warning('Среди эффектов типа события есть дубликаты');
                return false;
            }
            if (
                state.selectedSET.overflow_days != null &&
                state.selectedSET.overflow_count != null
            ) {
                const targetParamOverflowEffects = state.selectedSET.overflow_effects.filter(
                    (effi) => effi.target_param == curTargetParam,
                );
                if (targetParamOverflowEffects.length >= 2) {
                    messageApi.warning('Среди эффектов-санкций за злоупотребление есть дубликаты');
                    return false;
                }
            }
        }
        return true;
    }

    async function onSETsave(changedSET: TScheduleEventType) {
        state.selectedSET = changedSET;
        if (!validateSET()) return;
        /*if (state.SETmodalUseCase == "new") {
            state.scheduleEventTypeList.push({
                ...changedSET,
                id: state.scheduleEventTypeList.length
            });
        } else {
            state.scheduleEventTypeList = state.scheduleEventTypeList.map(seti => 
                seti.id == changedSET.id
                ? { ...changedSET }
                : seti
            );
        }
        console.log(state.scheduleEventTypeList);
        state.SETmodalOpen = false;
        state.selectedSET = null;
        */

        try {
            const tSET = await CRMAPIManager.request<ScheduleEventTypeResp>(async (api) => {
                if (state.SETmodalUseCase == 'edit') {
                    return await api.updateScheduleEventType(changedSET);
                } else {
                    return await api.createScheduleEventType(changedSET);
                }
            });
            if (tSET.errorMessages) throw tSET.errorMessages;
            await loadScheduleEventTypeList();
            state.SETmodalOpen = false;
            state.selectedSET = null;
            messageApi.success(
                `Тип события ${state.SETmodalUseCase == 'edit' ? 'создан' : 'сохранён'}`,
            );
        } catch (errors) {
            messageApi.error(
                `Ошибка при ${state.SETmodalUseCase == 'edit' ? 'сохранении' : 'создании'} типа событий`,
            );
            console.log(errors);
        }
    }

    async function saveAllowedSETchanges() {
        state.simulationOriginal.allowed_schedule_event_types = [
            ...state.simulation.allowed_schedule_event_types,
        ];
        messageApi.info('Тут будет сохранение изменений выборки разрешённых типов событий');
        return true;
    }

    function prepareChanges(): TBulkUpdate<TSimScheduleEvent> {
        return Object.keys(state.changes).map((changeKey, i) => {
            const change = state.changes[changeKey];
            return {
                action: change.change_type,
                index: i + 1,
                value:
                    change.change_type == 'delete'
                        ? { simulation_id: change.simulation_id, id: change.id }
                        : {
                              simulation_id: change.simulation_id,
                              id: change.id,
                              event_type_id: change.event_type_id,
                              event_worker_uid: change.event_worker_uid,
                              week: change.week,
                              day: change.day,
                              start: change.start,
                              duration: change.duration,
                              end: change.end,
                              name_override: change.name_override,
                              locked: change.locked,
                              created_at: change.created_at,
                              updated_at: change.updated_at,
                              deleted_at: change.deleted_at,
                          },
            };
        });
    }

    async function saveSimEventChanges() {
        /*
        console.log(state.changes);
        state.changes = {};
        message.info("Тут будет сохранение запланированных событий");
        */
        state.isLoading = true;
        let returnValue = false;
        // Bulk с операцией замены (delete + add в тайм-слоте) выдаёт 500,
        // временная замена на перебор
        const bulkBody = prepareChanges();
        /*
        try {
            const result = await CRMAPIManager.bulkRequest<TSimScheduleEvent>(
                bulkBody,
                async (api, _bulkBody) => {
                    return await api.bulkSimScheduleEvent(_bulkBody);
                },
                async (api, task_id) => {
                    return await api.bulkResultSimSheduleEvent(task_id);
                }
            );
            if (result.errorMessages) throw result.errorMessages;
            message.success("Изменения сохранены");
            await loadNecessary();
            returnValue = true;
        } catch (errors) {
            messageApi.error("Ошибка при сохранении, попробуйте ещё раз");
            console.log(errors);
        }
        */
        try {
            for (let i = 0; i < bulkBody.length; i++) {
                const change = bulkBody[i];
                switch (change.action) {
                    case 'add': {
                        const result = await CRMAPIManager.request<SimScheduleEventResp>(
                            async (api) => {
                                return api.createSimScheduleEvent(
                                    change.value as TSimScheduleEvent,
                                );
                            },
                        );
                        if (result.errorMessages) throw result.errorMessages;
                        break;
                    }
                    case 'update': {
                        const result = await CRMAPIManager.request<SimScheduleEventResp>(
                            async (api) => {
                                return api.updateSimScheduleEvent(
                                    change.value as TSimScheduleEvent,
                                );
                            },
                        );
                        if (result.errorMessages) throw result.errorMessages;
                        break;
                    }
                    case 'remove': {
                        const result = await CRMAPIManager.request<SimScheduleEventResp>(
                            async (api) => {
                                return api.removeSimScheduleEvent(
                                    (change.value as { simulation_id: number; id: number })
                                        .simulation_id,
                                    (change.value as { simulation_id: number; id: number }).id,
                                );
                            },
                        );
                        if (result.errorMessages) throw result.errorMessages;
                    }
                }
            }
            message.success('Изменения сохранены');
            await loadNecessary();
            returnValue = true;
        } catch (errors) {
            messageApi.error('Ошибка при сохранении, попробуйте ещё раз');
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    async function onSETrestore(schedule_event_type_id: TScheduleEventType['id']) {
        state.isLoading = true;
        try {
            const tSET = await CRMAPIManager.request<ScheduleEventTypeResp>(async (api) => {
                return await api.restoreScheduleEventType(schedule_event_type_id);
            });
            if (tSET.errorMessages) throw tSET.errorMessages;
            messageApi.success('Тип события восстановлен');
            await loadScheduleEventTypeList();
        } catch (errors) {
            messageApi.error('Ошибка при восстановлении типа события');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function validateSSET(item: TSimScheduleEvent): boolean {
        return true;
    }

    async function onSSETadd(item: TSimScheduleEvent) {
        if (!validateSSET(item)) return false;
        state.changes[item.id] = {
            ...item,
            change_type: 'add',
        };
        return true;
    }

    async function onSSETchange(item: TSimScheduleEvent) {
        if (!validateSSET(item)) return false;
        state.changes[item.id] = {
            ...item,
            change_type: 'update',
        };
        return true;
    }

    async function onSSETdelete(id: TSimScheduleEvent['id']) {
        if (Object.keys(state.changes).find((key) => +key == id)) {
            state.changes[id] = {
                ...state.changes[id],
                change_type: 'remove',
            };
        } else {
            const tSSET = state.scheduleEventList.find((sseti) => sseti.id == id);
            if (tSSET == undefined) {
                messageApi.error('Не найден оригинал удаляемого события');
                return;
            }
            state.changes[id] = {
                ...tSSET,
                change_type: 'remove',
            };
        }
    }

    function onAllowInSimClick(schedule_event_type_id: TScheduleEventType['id']) {
        if (state.simulation?.allowed_schedule_event_types.includes(schedule_event_type_id)) {
            state.simulation.allowed_schedule_event_types =
                state.simulation.allowed_schedule_event_types.filter(
                    (aseti) => aseti != schedule_event_type_id,
                );
        } else {
            state.simulation.allowed_schedule_event_types = [
                ...state.simulation.allowed_schedule_event_types,
                schedule_event_type_id,
            ];
        }
    }

    return (
        <MainLayout
            activeTab="schedule"
            additionalClass="profile-min-width"
            tabSet="constructor"
            showSearch={false}
        >
            <div className="schedule-container">
                {state.isLoading && <Loader />}
                {contextHolder}
                {useCase == ConstructorScheduleUseCases.ScheduleEventTypes && (
                    <PreventLeaving
                        anyChanges={anySETchanges}
                        onSave={saveAllowedSETchanges}
                    />
                )}
                {useCase == ConstructorScheduleUseCases.SimScheduleEvents && (
                    <PreventLeaving
                        anyChanges={anySSETchanges}
                        onSave={saveSimEventChanges}
                    />
                )}
                <div className="schedule-inner">
                    <Row className="schedule-actions">
                        <Col className="schedule-mode-selector">
                            <Radio.Group
                                onChange={(e) => {
                                    e.stopPropagation();
                                    if (
                                        e.target.value ==
                                        ConstructorScheduleUseCases.SimScheduleEvents
                                    ) {
                                        navigate(`/constructor/${simId}/schedule-events`);
                                        return;
                                    }
                                    if (
                                        e.target.value ==
                                        ConstructorScheduleUseCases.ScheduleEventTypes
                                    ) {
                                        navigate(`/constructor/${simId}/schedule-event-types`);
                                        return;
                                    }
                                }}
                                options={[
                                    {
                                        label: 'Календарь',
                                        value: ConstructorScheduleUseCases.SimScheduleEvents,
                                    },
                                    {
                                        label: 'Библиотека событий',
                                        value: ConstructorScheduleUseCases.ScheduleEventTypes,
                                    },
                                ]}
                                optionType="button"
                                value={useCase}
                            />
                        </Col>
                        {useCase == ConstructorScheduleUseCases.SimScheduleEvents && (
                            <Col className="p3 schedule-mode-save">
                                {state.simulation != null && anySSETchanges && (
                                    <Button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            saveSimEventChanges();
                                        }}
                                    >
                                        Сохранить изменения
                                    </Button>
                                )}
                            </Col>
                        )}
                        {useCase == ConstructorScheduleUseCases.ScheduleEventTypes && (
                            <Col>
                                <Radio.Group
                                    onChange={(e) => {
                                        e.stopPropagation();
                                        state.mode = e.target.value;
                                    }}
                                    options={[
                                        { label: 'Текущие', value: 'current' },
                                        { label: 'Корзина', value: 'deleted' },
                                        { label: 'Все', value: 'all' },
                                    ]}
                                    optionType="button"
                                    value={state.mode}
                                />
                            </Col>
                        )}
                        {useCase == ConstructorScheduleUseCases.ScheduleEventTypes && (
                            <Col className="p3 schedule-mode-save">
                                {state.simulation != null &&
                                    state.simulationOriginal != null &&
                                    anySETchanges && (
                                        <Button
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                saveAllowedSETchanges();
                                            }}
                                        >
                                            Сохранить выбор
                                        </Button>
                                    )}
                            </Col>
                        )}
                    </Row>
                    {useCase == ConstructorScheduleUseCases.SimScheduleEvents && (
                        <Schedule
                            changes={state.changes}
                            dayEndHour={DefaultTimeSettings.calendarDayEnd}
                            daysInAWeek={DefaultTimeSettings.daysInAWeek}
                            dayStartHour={DefaultTimeSettings.calendarDayStart}
                            disableEdit={disableEdit}
                            onSSETadd={onSSETadd}
                            onSSETchange={onSSETchange}
                            onSSETdelete={onSSETdelete}
                            scheduleEventTypeList={state.scheduleEventTypeList}
                            simScheduleEventList={state.scheduleEventList}
                            totalWeeks={state.simulation?.weeks}
                            useCase="Default"
                            workerList={state.workers}
                        />
                    )}
                    {useCase == ConstructorScheduleUseCases.ScheduleEventTypes && (
                        <div className="schedule-event-type-list">
                            {state.SETmodalOpen && state.selectedSET != null && (
                                <ScheduleEventTypeModal
                                    isLoading={state.isLoading}
                                    isOpen={state.SETmodalOpen}
                                    onDelete={
                                        state.SETmodalUseCase == 'new' ? undefined : onSETdelete
                                    }
                                    onSave={onSETsave}
                                    SET={state.selectedSET}
                                    setIsOpen={setSETmodalOpen}
                                    useCase={state.SETmodalUseCase}
                                />
                            )}
                            <ConfigProvider
                                theme={{
                                    token: GlobalConstants.ListGridSettings,
                                }}
                            >
                                <List
                                    dataSource={makeSETListItems()}
                                    grid={GlobalConstants.ListGridCols}
                                    loading={state.isLoading}
                                    locale={{
                                        emptyText: (
                                            <Col
                                                className="empty-text p3"
                                                flex={1}
                                            >
                                                <Row>Таких типов событий нет :)</Row>
                                            </Col>
                                        ),
                                    }}
                                    renderItem={(item: 'add' | TScheduleEventType) => {
                                        if (item == 'add')
                                            return (
                                                <div
                                                    className="schedule-event-type-list-add"
                                                    onClick={() => {
                                                        onSETaddClick();
                                                    }}
                                                >
                                                    <div className="add-icon" />
                                                </div>
                                            );
                                        else
                                            return (
                                                <div className="schedule-event-type-list-card">
                                                    <Row className="header-row">
                                                        <Col>
                                                            <Row>
                                                                <div className="p2-strong">
                                                                    ID {item.id}
                                                                </div>
                                                                <CopyButton
                                                                    textToCopy={`Тип календарного события #${item.id}`}
                                                                    textToShow="ID типа календарного события скопирован"
                                                                    size={24}
                                                                />
                                                            </Row>
                                                        </Col>
                                                        {item.deleted_at != null && (
                                                            <Col>
                                                                <FilterButton
                                                                    hex={Colors.Error.warm[500]}
                                                                    text="Удалён"
                                                                />
                                                            </Col>
                                                        )}
                                                    </Row>
                                                    <Row className="body-row">
                                                        <Col>
                                                            <Row>
                                                                <div className="p2-strong">
                                                                    {item.name}
                                                                </div>
                                                                <CopyButton
                                                                    textToCopy={`Тип календарного события \'${item.name}\'`}
                                                                    textToShow="Название типа календарного события скопировано"
                                                                    size={24}
                                                                />
                                                            </Row>
                                                        </Col>
                                                    </Row>
                                                    <Row className="p2 split-row">
                                                        <Col>
                                                            Длительность: {item.duration} часов
                                                        </Col>
                                                        <Col>
                                                            Масштаб:{' '}
                                                            {item.event_level == 'global'
                                                                ? 'симуляция'
                                                                : 'ПШЕ'}
                                                        </Col>
                                                    </Row>
                                                    <Row className="p2 split-row">
                                                        <Col>
                                                            Перерыв: {makeCooldownString(item)}
                                                        </Col>
                                                        <Col>
                                                            Злоупотребление:{' '}
                                                            {makeOverflowString(item)}
                                                        </Col>
                                                    </Row>
                                                    <Row className="p3 schedule-event-type-controls">
                                                        <Button
                                                            icon={<div className="edit-icon" />}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                onSETeditClick(item);
                                                            }}
                                                        />
                                                        {item.deleted_at != null && (
                                                            <Button
                                                                icon={
                                                                    <div className="restore-icon" />
                                                                }
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    onSETrestore(item.id);
                                                                }}
                                                            />
                                                        )}
                                                        <Checkbox
                                                            checked={state.simulation?.allowed_schedule_event_types.includes(
                                                                item.id,
                                                            )}
                                                            disabled={
                                                                disableEdit ||
                                                                item.deleted_at != null
                                                            }
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                onAllowInSimClick(item.id);
                                                            }}
                                                        >
                                                            В прохождении
                                                        </Checkbox>
                                                    </Row>
                                                </div>
                                            );
                                    }}
                                />
                            </ConfigProvider>
                        </div>
                    )}
                </div>
            </div>
        </MainLayout>
    );
};

export default ConstructorSchedule;
