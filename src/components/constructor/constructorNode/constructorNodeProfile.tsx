import { CRMAPIManager } from '@api/crmApiManager';
import MainLayout from '@components/ui/layout/mainLayout';
import { useReactive } from 'ahooks';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TSimTask } from 'types/simulation/simulationTask';
import {
    Row,
    Col,
    Button,
    message,
    Popconfirm,
    InputNumber,
    Slider,
    Collapse,
    Form,
    Input,
} from 'antd';
import { SimulationResp } from '@api/responseModels/simulations/simulationResponse';
import { TSimulation } from 'types/simulation/simulation';
import { SimTaskResp } from '@api/responseModels/simulations/simulationTasks/simulationTaskResponse';
import { Common } from '@classes/common';
import { Loader } from '@components/ui/loader/loader';
import { Permissions } from '@classes/permissions';
import { statColors } from '../constructorWorker/constructorWorkerList';
import { CopyButton } from '@components/ui/copyButton/copyButton';
import { PreventLeaving } from '@components/ui/preventLeaving/preventLeaving';
import { FilterButton } from '@components/ui/filterBtn/filterBtn';
import Colors from '@classes/colors';

import './constructorNodeProfile.scss';

type TState = {
    anyChanges: boolean;
    isLoading: boolean;
    simulation: TSimulation;
    skipPreventLeaving: boolean;
    task: TSimTask;
    useCase: 'create' | 'edit';
};

const ConstructorNodeProfile = (): JSX.Element => {
    const state = useReactive<TState>({
        anyChanges: false,
        isLoading: false,
        simulation: null,
        skipPreventLeaving: false,
        task: null,
        useCase: 'create',
    });
    const { simId, nodeId } = useParams();
    const navigate = useNavigate();
    const [messageApi, contextHolder] = message.useMessage();
    const [form] = Form.useForm();
    const disableEdit =
        state.simulation?.archived ||
        state.simulation?.deleted_at != null ||
        state.simulation?.published ||
        state.simulation?.finished;

    function updateTask(t: Partial<TSimTask>, notFromForm: boolean = false) {
        if (notFromForm) {
            state.task = { ...state.task, ...t };
        } else {
            state.task = { ...form.getFieldsValue(true) };
        }
        state.anyChanges = true;
    }

    async function loadExistingTask() {
        state.isLoading = true;
        try {
            const task = await CRMAPIManager.request<SimTaskResp>(async (api) => {
                return await api.getSimTaskById(+simId, +nodeId);
            });
            if (task.errorMessages) throw task.errorMessages;
            state.task = task.data.data;
            form.setFieldsValue(state.task);
            state.anyChanges = false;
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate(`/constructor/${simId}/tasks`);
            }
            messageApi.error('Ошибка при получении узла');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function loadParentSim() {
        state.isLoading = true;
        try {
            const sim = await CRMAPIManager.request<SimulationResp>(async (api) => {
                return await api.getSimulation(+simId);
            });
            if (sim.errorMessages) throw sim.errorMessages;
            if (sim.data.data.deleted_at != null) {
                navigate('/simulations');
                message.error('Работа в удалённой симуляции запрещена');
                return;
            }
            state.simulation = sim.data.data;
        } catch (errors) {
            if (errors?.message?.includes('404')) {
                navigate('/simulations');
            }
            messageApi.error('Ошибка при получении симуляции');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function setTaskTemplate() {
        state.task = {
            uid: null,
            simulation_id: +simId,
            id: null,
            name: '',
            description: '',
            milestone: false,
            est_workers: 1,
            est_duration: 1,
            est_budget: 1000,
            previous: [],
            following: [],
            stats_req: [
                0,
                0,
                0,
                0,
                0,
            ],
            grid_x: 1,
            grid_y: 1,
            deleted_at: null,
            created_at: Common.dateNowString(),
            updated_at: null,
        };
        form.setFieldsValue(state.task);
    }

    useEffect(() => {
        state.skipPreventLeaving = false;
        if (simId != undefined && Number.isNaN(+simId)) {
            if (Permissions.checkPermission(Permissions.SimulationList)) {
                navigate('/simulations');
            } else {
                navigate('/lk');
            }
            return;
        }
        loadParentSim().then(() => {
            if (state.simulation == null) return;
            if (nodeId != undefined) {
                state.useCase = 'edit';
                loadExistingTask();
            } else {
                setTaskTemplate();
            }
        });
    }, [nodeId]);

    async function saveTask() {
        state.isLoading = true;
        let returnValue = false;
        try {
            const task = await CRMAPIManager.request<SimTaskResp>(async (api) => {
                if (state.useCase == 'create') {
                    state.skipPreventLeaving = true;
                    return api.createSimTask(state.task);
                } else {
                    return api.updateSimTask(state.task);
                }
            });
            if (task.errorMessages) throw task.errorMessages;
            if (state.useCase == 'create') {
                navigate(`/constructor/${simId}/nodes/${task.data.data.id}`);
                message.success('Узел создан');
            } else {
                updateTask(task.data.data, true);
                form.setFieldsValue(state.task);
                state.anyChanges = false;
                message.success('Изменения сохранены');
            }
            returnValue = true;
        } catch (errors) {
            state.skipPreventLeaving = false;
            messageApi.error(
                `Ошибка при ${state.useCase == 'create' ? 'создании' : 'сохранении'} узла`,
            );
            console.log(errors);
        }
        state.isLoading = false;
        return returnValue;
    }

    async function deleteTask() {
        state.isLoading = true;
        try {
            const task = await CRMAPIManager.request<SimTaskResp>(async (api) => {
                return api.removeSimTask(+simId, state.task?.uid);
            });
            if (task.errorMessages) throw task.errorMessages;
            updateTask(task.data.data, true);
            form.setFieldsValue(state.task);
            state.anyChanges = false;
            messageApi.success('Узел был удалён');
        } catch (errors) {
            messageApi.error('Ошибка при удалении узла');
            console.log(errors);
        }
        state.isLoading = false;
    }

    async function restoreTask() {
        state.isLoading = true;
        try {
            const task = await CRMAPIManager.request<SimTaskResp>(async (api) => {
                return api.restoreSimTask(+simId, state.task?.uid);
            });
            if (task.errorMessages) throw task.errorMessages;
            updateTask(task.data.data, true);
            form.setFieldsValue(state.task);
            state.anyChanges = false;
            messageApi.success('Узел был восстановлен');
        } catch (errors) {
            messageApi.error('Ошибка при восстановлении узла');
            console.log(errors);
        }
        state.isLoading = false;
    }

    function cancelCreation() {
        navigate(`/constructor/${simId}/nodes`);
    }

    return (
        <MainLayout
            activeTab="nodes"
            additionalClass="profile-min-width"
            tabSet="constructor"
        >
            <div className="task-profile">
                {state.isLoading && <Loader />}
                {contextHolder}
                <PreventLeaving
                    anyChanges={state.anyChanges && !state.skipPreventLeaving}
                    onSave={saveTask}
                />
                <Col
                    flex={1}
                    className="task-card"
                >
                    <Row className="id-row">
                        <Col>
                            <Row>
                                <h3>ID {state.useCase == 'create' ? 'НОВЫЙ' : state.task?.id}</h3>
                                {state.useCase != 'create' && (
                                    <Col>
                                        <CopyButton
                                            textToCopy={`Узел #${state.task?.id} симуляции #${simId}`}
                                            textToShow="ID узла скопирован"
                                            size={36}
                                        />
                                    </Col>
                                )}
                            </Row>
                        </Col>
                        {state.task?.deleted_at != null && (
                            <Col>
                                <FilterButton
                                    hex={Colors.Error.warm[500]}
                                    text="Удалён"
                                />
                            </Col>
                        )}
                    </Row>
                    <Form
                        className="node-form"
                        form={form}
                        onFinish={saveTask}
                        onValuesChange={(cv) => updateTask(cv)}
                    >
                        <Row className="inputs-row">
                            <Form.Item
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: 'Пожалуйста, введите название',
                                    },
                                    {
                                        max: 50,
                                        message: 'Не больше 50 символов',
                                    },
                                ]}
                            >
                                <Input
                                    allowClear
                                    disabled={disableEdit}
                                    maxLength={50}
                                    placeholder="Введите название узла"
                                    showCount
                                />
                            </Form.Item>
                        </Row>
                        <Row className="inputs-row">
                            <Form.Item
                                name="description"
                                rules={[
                                    {
                                        max: 390,
                                        message: 'Не больше 390 символов',
                                    },
                                ]}
                            >
                                <Input.TextArea
                                    allowClear
                                    disabled={disableEdit}
                                    maxLength={390}
                                    placeholder="Введите описание"
                                    rows={3}
                                    showCount
                                />
                            </Form.Item>
                        </Row>
                        <Row className="multi-input-row">
                            <Col>
                                {state.simulation?.skills.map((skill, i) => (
                                    <Row
                                        className="sks-row"
                                        key={`sks-${i}`}
                                    >
                                        <Col
                                            className="labeled-input"
                                            flex={1}
                                        >
                                            <Row>
                                                <span className="p3">{skill}</span>
                                            </Row>
                                            <Row>
                                                {/*<InputNumber
                                                    max={6}
                                                    min={0}
                                                    onChange={(value) => updateTask({
                                                        stats_req: state.task.stats_req.map((_, j) => 
                                                            j==i ? value : _
                                                    )})}
                                                    placeholder="-"
                                                    required
                                                    type="number"
                                                    value={state.task?.stats_req?.[i]}
                                                />*/}
                                                <Form.Item
                                                    name={['stats_req', i + 1]}
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message:
                                                                'Выберите уровень характеристики',
                                                        },
                                                        {
                                                            type: 'integer',
                                                            max: 5,
                                                            min: 0,
                                                            message: 'От 0 до 5',
                                                        },
                                                    ]}
                                                >
                                                    <Slider
                                                        disabled={disableEdit}
                                                        marks={{
                                                            0: 0,
                                                            1: 1,
                                                            2: 2,
                                                            3: 3,
                                                            4: 4,
                                                            5: 5,
                                                        }}
                                                        max={5}
                                                        min={0}
                                                        step={1}
                                                        styles={{
                                                            track: {
                                                                background: statColors[i],
                                                            },
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Row>
                                        </Col>
                                    </Row>
                                ))}
                            </Col>
                            <Col>
                                <Row>
                                    <Col className="labeled-input">
                                        <Row>
                                            <span className="p3">Кол-во людей (план):</span>
                                        </Row>
                                        <Row>
                                            <Form.Item
                                                name="est_workers"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: 'Введите оптимальное кол-во ПШЕ',
                                                    },
                                                    {
                                                        type: 'integer',
                                                        max: 16,
                                                        min: 1,
                                                        message: 'От 1 до 16',
                                                    },
                                                ]}
                                            >
                                                <InputNumber
                                                    disabled={disableEdit}
                                                    max={16}
                                                    min={1}
                                                    suffix={
                                                        <div className="duration-suffix">чел.</div>
                                                    }
                                                />
                                            </Form.Item>
                                        </Row>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col className="labeled-input">
                                        <Row>
                                            <span className="p3">Продолжительность (план):</span>
                                        </Row>
                                        <Row>
                                            <Form.Item
                                                name="est_duration"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: 'Введите продолжительность',
                                                    },
                                                    {
                                                        type: 'integer',
                                                        max: 50,
                                                        min: 1,
                                                        message: 'От 1 до 50',
                                                    },
                                                ]}
                                            >
                                                <InputNumber
                                                    disabled={disableEdit}
                                                    max={50}
                                                    min={1}
                                                    suffix={
                                                        <div className="duration-suffix">дней</div>
                                                    }
                                                />
                                            </Form.Item>
                                        </Row>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col className="labeled-input">
                                        <Row>
                                            <span className="p3">Бюджет (план):</span>
                                        </Row>
                                        <Row>
                                            <Form.Item
                                                name="est_budget"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: 'Введите плановый бюджет',
                                                    },
                                                    {
                                                        type: 'integer',
                                                        max: 10e5,
                                                        min: 10e2,
                                                        message: 'От 1 тыс. до 1 млн.',
                                                    },
                                                ]}
                                            >
                                                <InputNumber
                                                    disabled={disableEdit}
                                                    max={10e5}
                                                    min={10e2}
                                                    step={10e2}
                                                    suffix={
                                                        <div className="duration-suffix">руб</div>
                                                    }
                                                />
                                            </Form.Item>
                                        </Row>
                                    </Col>
                                </Row>
                                {state.useCase != 'create' && (
                                    <Row className="info-row">
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Был создан:</span>
                                            </Row>
                                            <Row>
                                                <span className="p3 lighter-tone">
                                                    {state.useCase == 'edit'
                                                        ? Common.formatDateString(
                                                              state.task?.created_at,
                                                          )
                                                        : '-'}
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                )}
                                {state.useCase != 'create' && (
                                    <Row className="info-row">
                                        <Col className="labeled-input">
                                            <Row>
                                                <span className="p3">Последнее изменение:</span>
                                            </Row>
                                            <Row>
                                                <span className="p3 lighter-tone">
                                                    {state.useCase == 'edit'
                                                        ? Common.formatDateString(
                                                              state.task?.updated_at,
                                                          )
                                                        : '-'}
                                                </span>
                                            </Row>
                                        </Col>
                                    </Row>
                                )}
                            </Col>
                        </Row>
                        <Row>
                            <Col flex={1}>
                                <Collapse
                                    expandIcon={() => <div className="expand-icon" />}
                                    expandIconPosition="end"
                                    items={[
                                        {
                                            key: 'previous',
                                            label: <h6>Предыдущие узлы</h6>,
                                            children: (
                                                <Row
                                                    style={{ gap: '8px' }}
                                                    wrap
                                                >
                                                    {state.task?.previous.map((p) => {
                                                        return (
                                                            <Col
                                                                key={p}
                                                                className="link-task-card desc-s"
                                                                onClick={() =>
                                                                    navigate(
                                                                        `/constructor/${simId}/nodes/${p}`,
                                                                    )
                                                                }
                                                            >
                                                                <span>{`ID ${p}`}</span>
                                                                <span>Перейти</span>
                                                            </Col>
                                                        );
                                                    })}
                                                    {state.task?.previous.length == 0 && (
                                                        <span className="p3 gray">Отсутствуют</span>
                                                    )}
                                                </Row>
                                            ),
                                        },
                                        {
                                            key: 'following',
                                            label: <h6>Следующие узлы</h6>,
                                            children: (
                                                <Row
                                                    style={{ gap: '8px' }}
                                                    wrap
                                                >
                                                    {state.task?.following.map((p) => {
                                                        return (
                                                            <Col
                                                                key={p}
                                                                className="link-task-card desc-s"
                                                                onClick={() =>
                                                                    navigate(
                                                                        `/constructor/${simId}/nodes/${p}`,
                                                                    )
                                                                }
                                                            >
                                                                <span>{`ID ${p}`}</span>
                                                                <span>Перейти</span>
                                                            </Col>
                                                        );
                                                    })}
                                                    {state.task?.following.length == 0 && (
                                                        <span className="p3 gray">Отсутствуют</span>
                                                    )}
                                                </Row>
                                            ),
                                        },
                                    ]}
                                />
                            </Col>
                        </Row>
                        {state.useCase == 'create' ? (
                            <Row className="controls-row p2">
                                <Button
                                    disabled={disableEdit || state.isLoading}
                                    htmlType="submit"
                                >
                                    Сохранить
                                </Button>
                                <Button
                                    disabled={disableEdit || state.isLoading}
                                    onClick={cancelCreation}
                                >
                                    Отмена
                                </Button>
                            </Row>
                        ) : (
                            <Row className="controls-row p2">
                                {state.anyChanges && (
                                    <Button
                                        disabled={disableEdit || state.isLoading}
                                        htmlType="submit"
                                    >
                                        Сохранить
                                    </Button>
                                )}
                                {state.anyChanges && (
                                    <Button
                                        disabled={disableEdit || state.isLoading}
                                        onClick={cancelCreation}
                                    >
                                        Отмена
                                    </Button>
                                )}
                                {state.task?.deleted_at == null ? (
                                    <Popconfirm
                                        cancelText="Отмена"
                                        disabled={disableEdit}
                                        okText="Подтвердить"
                                        onConfirm={deleteTask}
                                        title="Узел будет удалён"
                                    >
                                        <Button disabled={disableEdit || state.isLoading}>
                                            Удалить
                                        </Button>
                                    </Popconfirm>
                                ) : (
                                    <Button
                                        disabled={disableEdit || state.isLoading}
                                        onClick={restoreTask}
                                    >
                                        Восстановить
                                    </Button>
                                )}
                            </Row>
                        )}
                    </Form>
                </Col>
            </div>
        </MainLayout>
    );
};

export default ConstructorNodeProfile;
