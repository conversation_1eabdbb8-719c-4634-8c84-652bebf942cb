import MainLayout from '@components/ui/layout/mainLayout';
import { observer } from 'mobx-react';
import { ChatPageUseCase, ChatsFullpage } from './chatsFullpage';

import './chatsPage.scss';

const ChatsPage = observer((): JSX.Element => {
    return (
        <MainLayout
            activeTab="Чаты"
            additionalClass="uni-fixed-height"
            tabSet={null}
        >
            <div className="chats-container">
                <ChatsFullpage useCase={ChatPageUseCase.Default} />
            </div>
        </MainLayout>
    );
});

export default ChatsPage;
