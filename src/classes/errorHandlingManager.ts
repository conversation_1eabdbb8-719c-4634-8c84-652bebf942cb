import { message } from 'antd';

export interface ApiError {
    statusCode: number;
    errorCode?: string;
    errorMessages?: string[];
}

export enum ErrorCodes {
    EmailIsAlreadyUsed = 'EmailIsAlreadyUsed', //400
    Forbidden = 'Forbidden', //403
    IncorrectCredentials = 'IncorrectCredentials', //400
    InvitationIsAlreadyActivated = 'InvitationIsAlreadyActivated', //400
    LoginIsAlreadyUsed = 'LoginIsAlreadyUsed', //400
    NotFound = 'NotFound', //404
    ServerError = 'ServerError', //500
    TokenExpired = 'TokenExpired', //400
    UnauthorizedError = 'UnauthorizedError', //401
    UserIsAlreadyUsed = 'UserIsAlreadyUsed', //400
}

export enum RUStrings {
    EmailIsAlreadyUsed = 'Email уже используется',
    Forbidden = 'Недостаточно прав',
    IncorrectCredentials = 'Неверные данные',
    InvitationIsAlreadyActivated = 'Приглашение уже принято',
    LoginIsAlreadyUsed = 'Логин уже используется',
    NotFound = 'Объект не найден',
    ServerError = 'Ошибка сервера',
    TokenExpired = 'Токен устарел',
    UnauthorizedError = 'Нет доступа',
}

export class ErrorHandlingManager {
    /**
     * Парсинг ответа API об ошибке в стандартизованный формат
     */
    static parseApiError(statusCode: number, data: any): ApiError {
        // Формат исключения сервера
        if (Object.keys(data).includes('exception')) {
            return {
                statusCode,
                errorCode: data?.errorCode,
                errorMessages: ['Ошибка сервера'],
            };
        }
        // Формат ошибок валидации
        else if (Object.keys(data).includes('errors')) {
            return {
                statusCode,
                errorCode: data?.errorCode,
                errorMessages: Object.values(data?.errors),
            };
        }
        // Стандартный формат ошибки
        else {
            return {
                statusCode,
                errorCode: data?.errorCode,
                errorMessages: Object.keys(ErrorCodes).includes(data?.errorCode)
                    ? [RUStrings[data?.errorCode]]
                    : [data?.errorCode],
            };
        }
    }

    /**
     * Создает стандартизированную ошибку API и выбрасывает ее
     * Не отображает пользователю - только создает объект ошибки для перехвата выше
     */
    static createAndThrow(error: ApiError): never {
        ErrorHandlingManager.handleApiError(error);
        throw new Error(ErrorHandlingManager.formatApiErrorMessage(error));
    }

    /**
     * Обработка ошибок API и отображение соответствующих сообщений пользователю
     * Должен вызываться только в одном месте - на уровне обработчика запросов
     */
    static handleApiError(error: ApiError | Error): void {
        if (error instanceof Error) {
            console.error('API Error:', error.message);
            message.error(error.message);
            return;
        }

        const errorMessages = error.errorMessages || ['Неопределенная ошибка'];
        console.error(`API Error (${error.statusCode}):`, errorMessages.join(', '));
        this.sendErrorList([errorMessages]);
    }

    static sendErrorList(errors: any) {
        try {
            errors.forEach((err) => message.error(err));
        } catch (e) {
            message.error(errors.message);
        }
    }

    /**
     * Создание сообщения об ошибке из объекта ApiError
     */
    static formatApiErrorMessage(error: ApiError): string {
        const messages = error.errorMessages?.join(', ') || 'Неопределенная ошибка';
        return `${messages} (${error.statusCode})`;
    }
}
