// src/hooks/useMergedRouteHandle.ts
import { RouteHandle } from '@components/app';
import { useMatches } from 'react-router-dom';

/**
 * Хук который возвращает единый, объединенный объект `handle` из всех
 * совпадающих маршрутов в иерархии. Это позволяет "наследовать"
 * свойства handle от родительских маршрутов.
 */
export function useMergedRouteHandle(): RouteHandle {
    const matches = useMatches();

    return matches.reduce((acc, match) => {
        if (match.handle) {
            Object.assign(acc, match.handle);
        }
        return acc;
    }, {});
}
