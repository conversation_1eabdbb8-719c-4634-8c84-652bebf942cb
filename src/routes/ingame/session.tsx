import { lazy } from 'react';
import { Navigate, Route } from 'react-router-dom';
const SessionDashboard = lazy(() => import('@components/ingame/sessionDashboard/sessionDashboard'));
const SessionGantt = lazy(() => import('@components/ingame/sessionGantt/sessionGantt'));
const SessionChats = lazy(() => import('@components/ingame/sessionChats/sessionChats'));
const SessionCalendar = lazy(() => import('@components/ingame/sessionCalendar/sessionCalendar'));
const SessionBudget = lazy(() => import('@components/ingame/sessionBudget/sessionBudget'));
const SessionWorkerList = lazy(() => import('@components/ingame/sessionWorkers/sessionWorkerList'));
const SessionWorkerProfile = lazy(
    () => import('@components/ingame/sessionWorkers/sessionWorkerProfile'),
);
const SessionTask = lazy(() => import('@components/ingame/sessionTask/sessionTask'));
const SessionGraph = lazy(() => import('@components/ingame/ingameGraph/ingameGraph'));
const SessionCharts = lazy(() => import('@components/ingame/charts/chartList'));
const ComplexTaskChartPage = lazy(
    () => import('@components/ingame/charts/complexTaskChart/complexTaskChartPage'),
);

export const sessionRoutes = (
    <>
        <Route
            path="session"
            element={<Navigate to="/session/desktop" />}
        />
        <Route
            path="session/dashboard"
            element={<SessionDashboard />}
        />
        <Route
            path="session/gantt"
            element={<SessionGantt />}
        />
        <Route
            path="session/chats"
            element={<SessionChats />}
        />
        <Route
            path="session/chats/:chatId"
            element={<SessionChats />}
        />
        <Route
            path="session/calendar"
            element={<SessionCalendar />}
        />
        <Route
            path="session/budget"
            element={<SessionBudget />}
        />
        <Route
            path="session/tasks/:sessionTaskId"
            element={<SessionTask />}
        />
        <Route
            path="session/workers"
            element={<SessionWorkerList />}
        />
        <Route
            path="session/workers/:sessionWorkerId"
            element={<SessionWorkerProfile />}
        />
        <Route
            path="session/graph"
            element={<SessionGraph />}
        />
        <Route
            path="session/charts"
            element={<SessionCharts />}
        />
        <Route
            path="sessions/charts/complex-task-chart"
            element={<ComplexTaskChartPage />}
        />
    </>
);
