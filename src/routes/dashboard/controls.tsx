import { lazy } from 'react';
import { ProtectedRoute } from '@components/protectedRoutes/protectedRoutes';
import { Route } from 'react-router-dom';
import { Permissions } from '@classes/permissions';
const SessionList = lazy(() => import('@components/sessions/sessionList'));
const SessionProfile = lazy(() => import('@components/sessions/sessionProfile'));
const SessionAssignmentList = lazy(
    () => import('@components/sessions/sessionAssignments/sessionAssignmentList'),
);
const SessionAssignmentProfile = lazy(
    () => import('@components/sessions/sessionAssignments/sessionAssignmentProfile'),
);

export const controlsRoutes = (
    <>
        <Route element={<ProtectedRoute requiredPermissions={[Permissions.SessionList]} />}>
            <Route
                path="controls/sessions"
                element={<SessionList />}
            />
        </Route>
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.SessionGet,
                        Permissions.SessionDelete,
                        Permissions.SessionAssignmentList,
                        Permissions.SessionAssignmentGet,
                        Permissions.SessionAssignmentCreate,
                        Permissions.SessionAssignmentDelete,
                        Permissions.SessionAssignmentBulkAdd,
                        Permissions.SessionAssignmentBulkResult,
                        Permissions.SimulationGet,
                        Permissions.UserList,
                        Permissions.UserGet,
                    ]}
                />
            }
        >
            <Route
                path="controls/sessions/:sessionId"
                element={<SessionProfile />}
            />
        </Route>
        <Route
            element={<ProtectedRoute requiredPermissions={[Permissions.SessionAssignmentList]} />}
        >
            <Route
                path="controls/assignments"
                element={<SessionAssignmentList />}
            />
        </Route>
        <Route
            element={<ProtectedRoute requiredPermissions={[Permissions.SessionAssignmentGet]} />}
        >
            <Route
                path="controls/assignments/:sessionAssignmentId"
                element={<SessionAssignmentProfile />}
            />
        </Route>
    </>
);
