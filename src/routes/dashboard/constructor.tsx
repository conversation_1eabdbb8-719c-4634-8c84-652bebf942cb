import { lazy } from 'react';
import { ProtectedRoute } from '@components/protectedRoutes/protectedRoutes';
import { Route } from 'react-router-dom';
import { Permissions } from '@classes/permissions';
import { ConstructorScheduleUseCases } from '@components/constructor/constructorSchedule/constructorSchedule';
const ConstructorSim = lazy(() => import('@components/constructor/constructorSim/constructorSim'));
const ConstructorGraph = lazy(
    () => import('@components/constructor/constructorGraph/constructorGraph'),
);
const ConstructorGantt = lazy(
    () => import('@components/constructor/constructorGantt/constructorGantt'),
);
const ConstructorNodeList = lazy(
    () => import('@components/constructor/constructorNode/constructorNodeList'),
);
const ConstructorNodeProfile = lazy(
    () => import('@components/constructor/constructorNode/constructorNodeProfile'),
);
const ConstructorWorkerList = lazy(
    () => import('@components/constructor/constructorWorker/constructorWorkerList'),
);
const ConstructorWorkerProfile = lazy(
    () => import('@components/constructor/constructorWorker/constructorWorkerProfile'),
);
const ConstructorEventList = lazy(
    () => import('@components/constructor/constructorEvent/constructorEventList'),
);
const ConsturctorEventProfile = lazy(
    () => import('@components/constructor/constructorEvent/constructorEventProfile'),
);
const ConstructorSchedule = lazy(
    () => import('@components/constructor/constructorSchedule/constructorSchedule'),
);

export const constructorRoutes = (
    <>
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.SimulationGet,
                        Permissions.SimulationCreate,
                        Permissions.SimulationUpdate,
                        Permissions.SimulationRestore,
                        Permissions.SimulationDelete,
                        Permissions.SimulationTest,
                    ]}
                />
            }
        >
            <Route
                path="constructor"
                element={<ConstructorSim />}
            />
            <Route
                path="constructor/:simId"
                element={<ConstructorSim />}
            />
        </Route>
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.SimulationGet,
                        Permissions.SimulationUpdate,
                        Permissions.SimulationTaskList,
                        Permissions.SimulationTaskGet,
                        Permissions.SimulationTaskCreate,
                        Permissions.SimulationTaskUpdate,
                        Permissions.SimulationTaskDelete,
                        Permissions.SimulationTaskRestore,
                        Permissions.SimulationTaskBulkAdd,
                        Permissions.SimulationTaskBulkResult,
                    ]}
                />
            }
        >
            <Route
                path="constructor/:simId/graph"
                element={<ConstructorGraph />}
            />
        </Route>
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.SimulationGet,
                        Permissions.SimulationTaskList,
                        Permissions.SimulationWorkerList,
                    ]}
                />
            }
        >
            <Route
                path="constructor/:simId/gantt"
                element={<ConstructorGantt />}
            />
        </Route>
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.SimulationGet,
                        Permissions.SimulationTaskList,
                    ]}
                />
            }
        >
            <Route
                path="constructor/:simId/nodes"
                element={<ConstructorNodeList />}
            />
        </Route>
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.SimulationGet,
                        Permissions.SimulationTaskCreate,
                        Permissions.SimulationTaskDelete,
                        Permissions.SimulationTaskGet,
                        Permissions.SimulationTaskRestore,
                        Permissions.SimulationTaskUpdate,
                    ]}
                />
            }
        >
            <Route
                path="constructor/:simId/nodes/new"
                element={<ConstructorNodeProfile />}
            />
            <Route
                path="constructor/:simId/nodes/:nodeId"
                element={<ConstructorNodeProfile />}
            />
        </Route>
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.SimulationGet,
                        Permissions.SimulationEventList,
                    ]}
                />
            }
        >
            <Route
                path="constructor/:simId/events"
                element={<ConstructorEventList />}
            />
        </Route>
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.SimulationEventGet,
                        Permissions.SimulationEventCreate,
                        Permissions.SimulationEventDelete,
                        Permissions.SimulationEventRestore,
                        Permissions.SimulationEventUpdate,
                        Permissions.SimulationGet,
                        Permissions.SimulationWorkerList,
                        Permissions.SimulationTaskList,
                    ]}
                />
            }
        >
            <Route
                path="constructor/:simId/events/new"
                element={<ConsturctorEventProfile />}
            />
            <Route
                path="constructor/:simId/events/:eventId"
                element={<ConsturctorEventProfile />}
            />
        </Route>
        <Route
            path="constructor/:simId/workers"
            element={<ConstructorWorkerList />}
        />
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.SimulationGet,
                        Permissions.SimulationWorkerCreate,
                        Permissions.SimulationWorkerDelete,
                        Permissions.SimulationWorkerGet,
                        Permissions.SimulationWorkerRestore,
                        Permissions.SimulationWorkerUpdate,
                    ]}
                />
            }
        >
            <Route
                path="constructor/:simId/workers/new"
                element={<ConstructorWorkerProfile />}
            />
            <Route
                path="constructor/:simId/workers/:workerId"
                element={<ConstructorWorkerProfile />}
            />
        </Route>
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.SimulationGet,
                        Permissions.SimulationWorkerList,
                        Permissions.SimulationScheduleEventList,
                        Permissions.SimulationScheduleEventGet,
                        Permissions.SimulationScheduleEventUpdate,
                        Permissions.SimulationScheduleEventCreate,
                        Permissions.SimulationScheduleEventDelete,
                        Permissions.SimulationScheduleEventBulkAdd,
                        Permissions.SimulationScheduleEventBulkResult,
                        Permissions.SimulationScheduleEventTypeList,
                        Permissions.SimulationScheduleEventTypeGet,
                        Permissions.SimulationScheduleEventTypeCreate,
                        Permissions.SimulationScheduleEventTypeUpdate,
                        Permissions.SimulationScheduleEventTypeDelete,
                        Permissions.SimulationScheduleEventTypeRestore,
                    ]}
                />
            }
        >
            <Route
                path="constructor/:simId/schedule-events"
                element={
                    <ConstructorSchedule useCase={ConstructorScheduleUseCases.SimScheduleEvents} />
                }
            />
            <Route
                path="constructor/:simId/schedule-event-types"
                element={
                    <ConstructorSchedule useCase={ConstructorScheduleUseCases.ScheduleEventTypes} />
                }
            />
        </Route>
    </>
);
