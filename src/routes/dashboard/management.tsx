import { lazy } from 'react';
import { Permissions } from '@classes/permissions';
import { ProtectedRoute } from '@components/protectedRoutes/protectedRoutes';
import { Route } from 'react-router-dom';
import { InvitationProfileUseCases } from '@components/invitations/invitationProfile';
const UserList = lazy(() => import('@components/user/userList'));
const UserProfile = lazy(() => import('@components/user/userProfile'));
const InvitationList = lazy(() => import('@components/invitations/invitationList'));
const InvitationProfile = lazy(() => import('@components/invitations/invitationProfile'));
const RolesPage = lazy(() => import('@components/roles/roles'));
const FiltersPage = lazy(() => import('@components/filters/filters'));

export const managementRoutes = (
    <>
        <Route element={<ProtectedRoute requiredPermissions={[Permissions.UserList]} />}>
            <Route
                path="management/users"
                element={<UserList />}
            />
        </Route>
        <Route
            path="management/users/:userId"
            element={<UserProfile />}
        />
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.UserList,
                        Permissions.UserGet,
                        Permissions.InvitationBulkAdd,
                        Permissions.InvitationBulkResult,
                        Permissions.InvitationGet,
                        Permissions.InvitationUpdate,
                        Permissions.InvitationCreate,
                        Permissions.InvitationRestore,
                        Permissions.InvitationDelete,
                        Permissions.InvitationList,
                    ]}
                />
            }
        >
            <Route
                path="management/invitations"
                element={<InvitationList />}
            />
        </Route>
        <Route element={<ProtectedRoute requiredPermissions={[Permissions.InvitationCreate]} />}>
            <Route
                path="management/invitations/:invitationId"
                element={<InvitationProfile useCase={InvitationProfileUseCases.Profile} />}
            />
            <Route
                path="management/invitations/new"
                element={<InvitationProfile useCase={InvitationProfileUseCases.New} />}
            />
        </Route>
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.RoleList,
                        Permissions.PermissionCategoryList,
                    ]}
                />
            }
        >
            <Route
                path="management/roles"
                element={<RolesPage />}
            />
            <Route
                path="management/filters"
                element={<FiltersPage />}
            />
        </Route>
    </>
);
