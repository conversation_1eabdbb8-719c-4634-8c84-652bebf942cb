import { Route } from 'react-router-dom';
import { lazy } from 'react';
const FontsTest = lazy(() => import('@components/tests/fonts'));
const ColorsTest = lazy(() => import('@components/tests/colors'));
const StoresTest = lazy(() => import('@components/tests/stores'));
const NetworkTest = lazy(() => import('@components/tests/network'));
const TableStatusesTest = lazy(() => import('@components/tests/tableStatuses'));

export const testsRoutes = (
    <>
        <Route
            path="tests/fonts"
            element={<FontsTest />}
        />
        <Route
            path="tests/colors"
            element={<ColorsTest />}
        />
        <Route
            path="tests/stores"
            element={<StoresTest />}
        />
        <Route
            path="tests/network"
            element={<NetworkTest />}
        />
        <Route
            path="tests/table-statuses"
            element={<TableStatusesTest />}
        />
    </>
);
