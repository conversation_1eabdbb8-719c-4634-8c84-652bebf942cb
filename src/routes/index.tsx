import { lazy } from 'react';
import { createBrowserRouter, createRoutesFromElements, Navigate, Route } from 'react-router-dom';
import { Permissions } from '@classes/permissions';
import { LayoutWrapper } from '@components/ui/layout/uniLayout';
import { simulationsRoutes } from './dashboard/simulations';
import { constructorRoutes } from './dashboard/constructor';
import { managementRoutes } from './dashboard/management';
import { controlsRoutes } from './dashboard/controls';
import { testsRoutes } from './dashboard/tests';
import { ProtectedRoute } from '@components/protectedRoutes/protectedRoutes';
import { sessionRoutes } from './ingame/session';
import { IngameUniLayout } from '@components/ingame/ingameLayout/ingameUniLayout';
const Login = lazy(() => import('@components/login/login'));
const InviteRegistration = lazy(() => import('@components/inviteRegistration/inviteRegistration'));
const NotificationList = lazy(() => import('@components/notifications/notificationList'));
const ChatsPage = lazy(() => import('@components/chats/chatsPage'));
const UserProfile = lazy(() => import('@components/user/userProfile'));
const Logout = lazy(() => import('@components/user/logout'));
const SessionDesktop = lazy(() => import('@components/ingame/sessionDesktop/sessionDesktop'));

export const router = createBrowserRouter(
    createRoutesFromElements(
        <Route path="/">
            {/* Публичные страницы */}
            <Route
                path="login"
                element={<Login />}
            />
            <Route
                path="invite/:inviteUid"
                element={<InviteRegistration />}
            />

            {/* Страницы дэшборда для управления */}
            <Route element={<LayoutWrapper />}>
                <Route
                    index
                    element={<Navigate to="/lk" />}
                />
                {simulationsRoutes}
                {constructorRoutes}
                {managementRoutes}
                {controlsRoutes}
                {testsRoutes}
                <Route
                    element={
                        <ProtectedRoute requiredPermissions={[Permissions.NotificationList]} />
                    }
                >
                    <Route
                        path="notifications"
                        element={<NotificationList />}
                    />
                </Route>
                <Route
                    path="chats"
                    element={<ChatsPage />}
                />
                <Route
                    path="chats/:chatId"
                    element={<ChatsPage />}
                />
                <Route
                    path="lk"
                    element={<UserProfile />}
                />
                <Route
                    path="logout"
                    element={<Logout />}
                />
                <Route
                    path="*"
                    element={<Navigate to="lk" />}
                />
            </Route>

            {/* Роуты страниц внутри игровой сессии */}
            <Route
                path="session/desktop"
                element={<SessionDesktop />}
            />
            <Route element={<IngameUniLayout />}>{sessionRoutes}</Route>
        </Route>,
    ),
);
